(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[879],{5695:(e,a,s)=>{"use strict";var r=s(8999);s.o(r,"useParams")&&s.d(a,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(a,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(a,{useRouter:function(){return r.useRouter}})},7899:(e,a,s)=>{Promise.resolve().then(s.bind(s,9349))},9349:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>t});var r=s(5155),n=s(2115),l=s(5695);function t(){let[e,a]=(0,n.useState)({first_name:"",last_name:"",username:"",email:"",password:"",confirmPassword:""}),[s,t]=(0,n.useState)(""),[i,o]=(0,n.useState)(!1),d=(0,l.useRouter)(),m=s=>{a({...e,[s.target.name]:s.target.value})},c=async a=>{if(a.preventDefault(),t(""),e.password!==e.confirmPassword)return void t("Passwords do not match");o(!0);try{let a=await fetch("https://agenticairi-app6.exlservice.com/AITrainerbackend/api/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({first_name:e.first_name,last_name:e.last_name,username:e.username,email:e.email,password:e.password})}),s=await a.json();if(!a.ok)throw Error(s.error||"Registration failed");d.push("/signin")}catch(e){t(e.message)}finally{o(!1)}};return(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-[#fafbfc]",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center mb-8",children:[(0,r.jsx)("div",{className:"bg-orange-500 rounded-lg p-3 mb-4",children:(0,r.jsx)("svg",{width:"32",height:"32",fill:"white",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"})})}),(0,r.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"Create your account"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Join the AI Trainer platform"})]}),(0,r.jsxs)("form",{onSubmit:c,className:"bg-white p-8 rounded-lg shadow-md w-full max-w-md",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Sign Up"}),(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"Create your account to start learning or teaching"}),(0,r.jsxs)("div",{className:"mb-4 flex gap-2",children:[(0,r.jsxs)("div",{className:"w-1/2",children:[(0,r.jsx)("label",{className:"block mb-1 font-medium",children:"First Name"}),(0,r.jsx)("input",{type:"text",name:"first_name",className:"w-full border rounded px-3 py-2",value:e.first_name,onChange:m,required:!0,placeholder:"John"})]}),(0,r.jsxs)("div",{className:"w-1/2",children:[(0,r.jsx)("label",{className:"block mb-1 font-medium",children:"Last Name"}),(0,r.jsx)("input",{type:"text",name:"last_name",className:"w-full border rounded px-3 py-2",value:e.last_name,onChange:m,required:!0,placeholder:"Doe"})]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block mb-1 font-medium",children:"Username"}),(0,r.jsx)("input",{type:"text",name:"username",className:"w-full border rounded px-3 py-2",value:e.username,onChange:m,required:!0,placeholder:"johndoe"})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block mb-1 font-medium",children:"Email"}),(0,r.jsx)("input",{type:"email",name:"email",className:"w-full border rounded px-3 py-2",value:e.email,onChange:m,required:!0,placeholder:"<EMAIL>"})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block mb-1 font-medium",children:"I want to"}),(0,r.jsx)("select",{className:"w-full border rounded px-3 py-2 bg-white",disabled:!0,children:(0,r.jsx)("option",{children:"Learn (take courses)"})})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block mb-1 font-medium",children:"Password"}),(0,r.jsx)("input",{type:"password",name:"password",className:"w-full border rounded px-3 py-2",value:e.password,onChange:m,required:!0,placeholder:"Enter your password"})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block mb-1 font-medium",children:"Confirm Password"}),(0,r.jsx)("input",{type:"password",name:"confirmPassword",className:"w-full border rounded px-3 py-2",value:e.confirmPassword,onChange:m,required:!0,placeholder:"Confirm your password"})]}),s&&(0,r.jsx)("div",{className:"text-red-500 mb-2",children:s}),(0,r.jsx)("button",{type:"submit",className:"w-full bg-orange-500 text-white py-2 rounded mt-2 hover:bg-orange-600 transition",disabled:i,children:i?"Creating Account...":"Create Account"}),(0,r.jsxs)("div",{className:"text-center mt-4 text-gray-600",children:["Already have an account? ",(0,r.jsx)("a",{href:"/signin",className:"text-orange-500 hover:underline",children:"Sign in"})]})]})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[441,684,358],()=>a(7899)),_N_E=e.O()}]);