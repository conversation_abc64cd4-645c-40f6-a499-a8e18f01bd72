(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[217],{2589:(e,s,a)=>{Promise.resolve().then(a.bind(a,3807))},3807:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>l});var t=a(5155),r=a(2115),n=a(5695);function l(){let[e,s]=(0,r.useState)(""),[a,l]=(0,r.useState)(""),[i,o]=(0,r.useState)(""),[c,d]=(0,r.useState)(!1),u=(0,n.useRouter)(),m=async s=>{s.preventDefault(),d(!0),o("");try{let s=await fetch("https://agenticairi-app6.exlservice.com/AITrainerbackend/api/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:a})}),t=await s.json();if(!s.ok)throw Error(t.error||"Login failed");localStorage.setItem("token",t.token),"admin"===t.user.role?u.push("/admin"):u.push("/courses")}catch(e){o(e.message)}finally{d(!1)}};return(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-[#fafbfc]",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center mb-8",children:[(0,t.jsx)("div",{className:"bg-orange-500 rounded-lg p-3 mb-4",children:(0,t.jsx)("svg",{width:"32",height:"32",fill:"white",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"})})}),(0,t.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"Welcome back"}),(0,t.jsx)("p",{className:"text-gray-500",children:"Sign in to your AI Trainer account"})]}),(0,t.jsxs)("form",{onSubmit:m,className:"bg-white p-8 rounded-lg shadow-md w-full max-w-md",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Sign In"}),(0,t.jsx)("p",{className:"text-gray-500 mb-4",children:"Enter your email and password to access your account"}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block mb-1 font-medium",children:"Email"}),(0,t.jsx)("input",{type:"email",className:"w-full border rounded px-3 py-2",value:e,onChange:e=>s(e.target.value),required:!0,placeholder:"Enter your email"})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block mb-1 font-medium",children:"Password"}),(0,t.jsx)("input",{type:"password",className:"w-full border rounded px-3 py-2",value:a,onChange:e=>l(e.target.value),required:!0,placeholder:"Enter your password"})]}),i&&(0,t.jsx)("div",{className:"text-red-500 mb-2",children:i}),(0,t.jsx)("button",{type:"submit",className:"w-full bg-orange-500 text-white py-2 rounded mt-2 hover:bg-orange-600 transition",disabled:c,children:c?"Signing In...":"Sign In"}),(0,t.jsxs)("div",{className:"text-center mt-4 text-gray-600",children:["Don't have an account? ",(0,t.jsx)("a",{href:"/signup",className:"text-orange-500 hover:underline",children:"Sign up"})]})]})]})}},5695:(e,s,a)=>{"use strict";var t=a(8999);a.o(t,"useParams")&&a.d(s,{useParams:function(){return t.useParams}}),a.o(t,"usePathname")&&a.d(s,{usePathname:function(){return t.usePathname}}),a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}})}},e=>{var s=s=>e(e.s=s);e.O(0,[441,684,358],()=>s(2589)),_N_E=e.O()}]);