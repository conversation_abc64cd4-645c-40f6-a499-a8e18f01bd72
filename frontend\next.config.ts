import type { NextConfig } from "next";
import path from "path";

const nextConfig: NextConfig = {
  // For IIS deployment - generate static files
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true, // Required for static export
  },

  // Skip static generation for dynamic routes
  skipTrailingSlashRedirect: true,
  
  // Move outputFileTracingRoot to top level (no longer experimental)
  outputFileTracingRoot: path.join(__dirname, '../'),
  
  // API rewrites for development (optional)
  // async rewrites() {
  //   return [
  //     {
  //       source: '/api/:path*',
  //       //destination: 'http://localhost:5005/api/:path*', // Fallback for dev

  //      // https://agenticairi-app6.exlservice.com/signin/
  //     },
  //   ];
  // },
  
  // Optimize for production (swcMinify is enabled by default in Next.js 15)
  compress: true,
  
  // Handle environment variables
  env: {
    NEXT_PUBLIC_END_POINT: process.env.NEXT_PUBLIC_END_POINT,
  },
  
  // TypeScript and path resolution
  typescript: {
    ignoreBuildErrors: true, // Temporarily ignore TS errors during build
  },
  
  // ESLint configuration
  eslint: {
    ignoreDuringBuilds: true, // Ignore ESLint errors during build
  },
};

export default nextConfig;