// frontend/server.js
const express = require('express');
const next = require('next');

const dev = process.env.NODE_ENV !== 'production';
const app = next({ dev });
const handle = app.getRequestHandler();

// Import your bacdckend routes
const backendRoutes = require('../backend/dist/routes'); // Adjust path

app.prepare().then(() => {
  const server = express();

  // Use your existing backend API routes
  server.use('/api', backendRoutes);

  // Handle everything else with Next.js
  server.all('*', (req, res) => {
    return handle(req, res);
  });

  const PORT = process.env.PORT || 3000;
  server.listen(PORT, () => {
    console.log(`> Ready on http://localhost:${PORT}`);
  });
});