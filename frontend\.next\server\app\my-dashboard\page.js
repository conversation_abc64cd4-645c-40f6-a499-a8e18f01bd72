(()=>{var e={};e.id=432,e.ids=[432],e.modules={440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},474:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=t(5239),a=t(8088),n=t(8170),i=t.n(n),l=t(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let c={children:["",{children:["my-dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,5760)),"C:\\Projects\\AI-Trainer\\frontend\\src\\app\\my-dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,8429)),"C:\\Projects\\AI-Trainer\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Projects\\AI-Trainer\\frontend\\src\\app\\my-dashboard\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/my-dashboard/page",pathname:"/my-dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1820:e=>{"use strict";e.exports=require("os")},2722:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var r=t(687),a=t(9190),n=t(3210),i=t(5885),l=t(7e3);function o(){let[e,s]=(0,n.useState)(null),[t,o]=(0,n.useState)(!0),[c,d]=(0,n.useState)(null),[x,m]=(0,n.useState)(null);if(x?.id,t)return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-[#fafbfc]",children:[(0,r.jsx)(a.default,{}),(0,r.jsx)("main",{className:"flex-grow container mx-auto px-4 py-8",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-lg text-gray-600",children:"Loading dashboard..."})})})]});if(c||!e)return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-[#fafbfc]",children:[(0,r.jsx)(a.default,{}),(0,r.jsx)("main",{className:"flex-grow container mx-auto px-4 py-8",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-lg text-red-600",children:c||"Failed to load dashboard"})})})]});let{statistics:u,courses:p}=e;return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-[#fafbfc]",children:[(0,r.jsx)(a.default,{}),(0,r.jsxs)("main",{className:"flex-grow container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"My Learning Dashboard"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Track your progress and continue your learning journey"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6 border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"bg-orange-100 p-3 rounded-lg",children:(0,r.jsx)(i.g,{icon:l.ReK,className:"text-orange-600 text-xl"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:u.in_progress_courses}),(0,r.jsx)("p",{className:"text-gray-600",children:"Courses in Progress"})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6 border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"bg-green-100 p-3 rounded-lg",children:(0,r.jsx)(i.g,{icon:l.SGM,className:"text-green-600 text-xl"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:u.completed_courses}),(0,r.jsx)("p",{className:"text-gray-600",children:"Completed Courses"})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6 border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"bg-purple-100 p-3 rounded-lg",children:(0,r.jsx)(i.g,{icon:l.fmL,className:"text-purple-600 text-xl"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:u.certificates_earned}),(0,r.jsx)("p",{className:"text-gray-600",children:"Certificates Earned"})]})]})})]}),p.length>0?(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Your Courses"})}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Course"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Progress"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Accessed"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Action"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:p.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[e.domain," • ",e.level]}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:[e.estimated_hours," hours"]})]})}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[e.progress_percentage,"%"]}),(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:[e.completed_modules," of ",e.total_modules," modules"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:`h-2 rounded-full transition-all duration-300 ${"completed"===e.status?"bg-green-500":"bg-orange-500"}`,style:{width:`${e.progress_percentage}%`}})})]})}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${"completed"===e.status?"bg-green-100 text-green-800":"in_progress"===e.status?"bg-orange-100 text-orange-800":"bg-gray-100 text-gray-800"}`,children:"completed"===e.status?"Completed":"in_progress"===e.status?"In Progress":"Not Started"})}),(0,r.jsx)("td",{className:"px-6 py-4 text-sm text-gray-500",children:e.last_accessed_at?new Date(e.last_accessed_at).toLocaleDateString():"Never"}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsxs)("a",{href:`/courses/${e.course_id}`,className:"inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 transition-colors",children:[(0,r.jsx)(i.g,{icon:l.ijD,className:"mr-1"}),"completed"===e.status?"Review":"Continue"]})})]},e.course_id))})]})})]}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center",children:[(0,r.jsx)(i.g,{icon:l.ReK,className:"text-gray-400 text-4xl mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No courses started yet"}),(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"Start learning by exploring our course catalog"}),(0,r.jsx)("a",{href:"/courses",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 transition-colors",children:"Browse Courses"})]})]})]})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},5760:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\AI-Trainer\\\\frontend\\\\src\\\\app\\\\my-dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\AI-Trainer\\frontend\\src\\app\\my-dashboard\\page.tsx","default")},5952:(e,s,t)=>{Promise.resolve().then(t.bind(t,5760))},7910:e=>{"use strict";e.exports=require("stream")},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9190:(e,s,t)=>{"use strict";t.d(s,{default:()=>c});var r=t(687),a=t(3210),n=t(5885),i=t(7e3),l=t(7346),o=t(7351);let c=()=>{let[e,s]=(0,a.useState)(!1),[t,c]=(0,a.useState)(null),d=(0,a.useRef)(null),x=e=>{try{let s=e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),t=decodeURIComponent(atob(s).split("").map(function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(t)}catch(e){return console.error("Error decoding JWT:",e),null}};(0,a.useEffect)(()=>{let e=localStorage.getItem("token");if(e){let s=x(e);s&&c({id:s.id,email:s.email,username:s.username,role:s.role,first_name:s.first_name||s.username,last_name:s.last_name||""})}},[]);let m=(e,s)=>(e?e.charAt(0).toUpperCase():"")+(s?s.charAt(0).toUpperCase():"")||"U";return(0,a.useEffect)(()=>{let e=e=>{d.current&&!d.current.contains(e.target)&&s(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,r.jsxs)("nav",{className:"bg-white p-4 shadow-md",children:[(0,r.jsxs)("div",{className:"container mx-auto flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"bg-orange-500 rounded-xl p-2 flex items-center justify-center rounded-lg",children:(0,r.jsx)(o.A,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-orange-500 text-lg font-bold",children:"EX"}),(0,r.jsx)("span",{className:"text-[#005071] text-lg font-bold",children:"Learn"})]})]}),(0,r.jsx)("div",{className:"flex-grow flex justify-center mx-4",children:(0,r.jsx)("div",{className:"relative w-full max-w-[280px]",children:(0,r.jsx)("input",{type:"text",placeholder:"Search courses, topics, or instructors...",className:"w-full pl-3 pr-3 py-1 rounded-full border border-transparent focus:outline-none focus:ring-1 focus:ring-orange-100 text-xs placeholder-gray-400"})})}),(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,r.jsx)(l.A,{}),(0,r.jsx)("div",{className:"relative",ref:d,children:t?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 cursor-pointer hover:bg-gray-50 rounded-lg px-2 py-1 transition-colors duration-200",onClick:()=>{s(!e)},children:[(0,r.jsx)("div",{className:"bg-orange-500 text-white rounded-full h-8 w-8 flex items-center justify-center font-medium",children:m(t.first_name,t.last_name)}),(0,r.jsx)("span",{className:"text-gray-700 text-sm font-medium",children:t.first_name}),(0,r.jsx)(n.g,{icon:i.Jt$,className:`text-gray-500 text-xs transition-transform duration-200 ${e?"rotate-180":""}`})]}),e&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50",children:[(0,r.jsx)("div",{className:"px-4 py-3 border-b border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"bg-orange-500 text-white rounded-full h-10 w-10 flex items-center justify-center font-medium",children:m(t.first_name,t.last_name)}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[t.first_name," ",t.last_name]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:t.email})]})]})}),(0,r.jsxs)("div",{className:"py-1",children:[(0,r.jsxs)("button",{className:"w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200",onClick:()=>{s(!1)},children:[(0,r.jsx)(n.g,{icon:i.X46,className:"mr-3 text-gray-400"}),"Profile"]}),(0,r.jsxs)("button",{className:"w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200",onClick:()=>{localStorage.removeItem("token"),sessionStorage.removeItem("token"),localStorage.removeItem("authToken"),sessionStorage.removeItem("authToken"),c(null),window.location.href="/signin"},children:[(0,r.jsx)(n.g,{icon:i.GxD,className:"mr-3 text-red-500"}),"Log out"]})]})]})]}):(0,r.jsxs)("a",{href:"/signin",className:"flex items-center space-x-2 bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors duration-200",children:[(0,r.jsx)(n.g,{icon:i.X46,className:"text-sm"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Sign In"})]})})]})]}),(0,r.jsx)("div",{className:"container mx-auto mt-4",children:(0,r.jsxs)("ul",{className:"flex space-x-8",children:[(0,r.jsx)("li",{children:(0,r.jsxs)("a",{href:"/",className:"flex items-center space-x-2 text-orange-500 border-b-2 border-orange-500 pb-2",children:[(0,r.jsx)(n.g,{icon:i.v02,className:"text-sm"}),(0,r.jsx)("span",{children:"Home"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)("a",{href:"/my-dashboard",className:"flex items-center space-x-2 text-gray-700 hover:text-orange-500",children:[(0,r.jsx)(n.g,{icon:i.xiI,className:"text-sm"}),(0,r.jsx)("span",{children:"My Dashboard"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)("a",{href:"/my-learning",className:"flex items-center space-x-2 text-gray-700 hover:text-orange-500",children:[(0,r.jsx)(n.g,{icon:i.ReK,className:"text-sm"}),(0,r.jsx)("span",{children:"My Learning"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)("a",{href:"/my-certificates",className:"flex items-center space-x-2 text-gray-700 hover:text-orange-500",children:[(0,r.jsx)(n.g,{icon:i.fmL,className:"text-sm"}),(0,r.jsx)("span",{children:"Certificates"})]})})]})})]})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9504:(e,s,t)=>{Promise.resolve().then(t.bind(t,2722))},9551:e=>{"use strict";e.exports=require("url")},9646:e=>{"use strict";e.exports=require("child_process")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,773,658,114],()=>t(474));module.exports=r})();