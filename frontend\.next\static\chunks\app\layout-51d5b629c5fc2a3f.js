(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{285:(e,t,s)=>{"use strict";s.d(t,{$:()=>o});var a=s(5155);s(2115);var r=s(9708),n=s(2085),i=s(9434);let l=(0,n.F)("cursor-pointer inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"underline-offset-4 hover:underline text-primary",brand:"bg-brand-blue text-white border border-brand-blue hover:bg-brand-blue/90 hover:border-brand-blue/80",sucess:"bg-green-600 text-white border border-green-700 hover:bg-green-600/90 hover:border-green-600/80",warning:"bg-amber-400 text-gray-900 border border-yellow-500 hover:bg-yellow-400/90 hover:border-yellow-400/80"},size:{default:"h-10 py-2 px-4",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-md",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:s,size:n,asChild:o=!1,...c}=e,d=o?r.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,i.cn)(l({variant:s,size:n,className:t})),...c})}},347:()=>{},809:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2093,23)),Promise.resolve().then(s.t.bind(s,7735,23)),Promise.resolve().then(s.t.bind(s,4842,23)),Promise.resolve().then(s.bind(s,7535)),Promise.resolve().then(s.t.bind(s,347,23)),Promise.resolve().then(s.t.bind(s,3244,23))},2093:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},3244:()=>{},4842:e=>{e.exports={style:{fontFamily:"'Poppins', 'Poppins Fallback'",fontStyle:"normal"},className:"__className_6bee3b",variable:"__variable_6bee3b"}},5695:(e,t,s)=>{"use strict";var a=s(8999);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}})},7434:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7470:(e,t,s)=>{"use strict";s.d(t,{E:()=>o,p:()=>l});var a=s(5155),r=s(2115),n=s(4298);let i=(0,r.createContext)(void 0),l=e=>{let{children:t}=e,[s,l]=(0,r.useState)([]),[o,c]=(0,r.useState)(!1),[d,m]=(0,r.useState)(null);(0,r.useEffect)(()=>{let e=(0,n.io)("https://agenticairi-app6.exlservice.com/AITrainerbackend",{transports:["websocket","polling"],autoConnect:!0});return m(e),e.on("connect",()=>{console.log("Connected to notification server"),c(!0);let t=localStorage.getItem("token");if(t)try{let s=JSON.parse(atob(t.split(".")[1]));e.emit("join_user",s.id)}catch(e){console.error("Error decoding token:",e)}}),e.on("disconnect",()=>{console.log("Disconnected from notification server"),c(!1)}),e.on("new_notification",e=>{console.log("New notification received:",e),l(t=>[e,...t]),"granted"===Notification.permission&&new Notification(e.title,{body:e.message,icon:"/favicon.ico"})}),"default"===Notification.permission&&Notification.requestPermission(),x(),()=>{e.close()}},[]);let x=async()=>{try{let e=localStorage.getItem("token");if(!e)return;let t=await fetch("https://agenticairi-app6.exlservice.com/AITrainerbackend/api/notifications",{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}});if(t.ok){let e=await t.json();l(e)}}catch(e){console.error("Error fetching notifications:",e)}},u=s.filter(e=>!e.read).length;return(0,a.jsx)(i.Provider,{value:{notifications:s,unreadCount:u,isConnected:o,markAsRead:e=>{l(t=>t.map(t=>t.id===e?{...t,read:!0}:t))},markAllAsRead:()=>{l(e=>e.map(e=>({...e,read:!0})))},clearNotifications:()=>{l([])}},children:t})},o=()=>{let e=(0,r.useContext)(i);if(void 0===e)throw Error("useNotifications must be used within a NotificationProvider");return e}},7535:(e,t,s)=>{"use strict";s.d(t,{default:()=>N});var a=s(5155),r=s(2115),n=s(285),i=s(7949),l=s(9946);let o=(0,l.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),c=(0,l.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var d=s(7434);let m=(0,l.A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),x=(0,l.A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),u=(0,l.A)("layers",[["path",{d:"M12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83z",key:"zw3jo"}],["path",{d:"M2 12a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 12",key:"1wduqc"}],["path",{d:"M2 17a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 17",key:"kqbvx6"}]]),h=[{label:"Course Builder",icon:(0,a.jsx)(d.A,{className:"w-5 h-5"}),href:"/admin/upload"},{label:"AI Summarization",icon:(0,a.jsx)(m,{className:"w-5 h-5"}),href:"/admin/ai-summarization"},{label:"Avatar & Video",icon:(0,a.jsx)(x,{className:"w-5 h-5"}),href:"/admin/avatar-video"},{label:"Course Builder",icon:(0,a.jsx)(u,{className:"w-5 h-5"}),href:"/admin/course-builder"}];function f(e){let{sidebarExpanded:t,setSidebarExpanded:s,activePanel:r,setActivePanel:l}=e;return(0,a.jsxs)("aside",{className:"bg-white border-r border-gray-200 shadow-sm transition-all duration-300 ease-in-out flex flex-col ".concat(t?"w-48":"w-16"),children:[(0,a.jsxs)("div",{className:"py-2 px-4 border-b border-gray-200 flex items-center ".concat(t?"justify-between":"justify-center"),children:[t&&(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"bg-orange-500 rounded-xl p-2 flex items-center justify-center rounded-lg",children:(0,a.jsx)(i.A,{className:"w-6 h-6 text-white"})}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-orange-500 text-lg font-bold",children:"EX"}),(0,a.jsx)("span",{className:"text-[#005071] text-lg font-bold",children:"Learn"})]})]}),(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>s(!t),className:"p-2 hover:bg-gray-100","aria-label":t?"Collapse sidebar":"Expand sidebar",children:t?(0,a.jsx)(o,{className:"w-5 h-5"}):(0,a.jsx)(c,{className:"w-5 h-5"})})]}),(0,a.jsx)("nav",{className:"flex-1 p-4",children:(0,a.jsx)("div",{className:"space-y-2",children:h.map(e=>(0,a.jsxs)("a",{href:e.href,className:"flex items-center gap-3 py-1.5 px-3 rounded-lg text-[#005071] hover:text-white hover:bg-[#005071] transition-colors group ".concat(t?"justify-start":"justify-center"),title:t?void 0:e.label,children:[(0,a.jsx)("span",{className:"transition-colors",children:e.icon}),t&&(0,a.jsx)("span",{className:"text-xs font-medium",children:e.label})]},e.href))})})]})}var g=s(7470),b=s(7489),p=s(3578),v=s(7662),j=s(5695);let y=e=>{let{sidebarExpanded:t}=e,[s,l]=(0,r.useState)(!1),[o,c]=(0,r.useState)(null),d=(0,r.useRef)(null),m=(0,j.useRouter)(),x=e=>{try{let t=e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),s=decodeURIComponent(atob(t).split("").map(function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(s)}catch(e){return console.error("Error decoding JWT:",e),null}};(0,r.useEffect)(()=>{let e=localStorage.getItem("token");if(e){let t=x(e);t&&c({id:t.id,email:t.email,username:t.username,role:t.role,first_name:t.first_name||t.username,last_name:t.last_name||""})}},[]);let u=(e,t)=>(e?e.charAt(0).toUpperCase():"")+(t?t.charAt(0).toUpperCase():"")||"U";return(0,r.useEffect)(()=>{let e=e=>{d.current&&!d.current.contains(e.target)&&l(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,a.jsx)("nav",{className:"bg-white shadow-sm border-b sticky top-0 z-30",children:(0,a.jsxs)("div",{className:"container mx-auto flex justify-between items-center py-1 px-4",children:[!t&&(0,a.jsxs)("div",{className:"flex items-center space-x-3 absolute left-2",children:[(0,a.jsx)("div",{className:"bg-orange-500 rounded-xl p-2 flex items-center justify-center rounded-lg",children:(0,a.jsx)(i.A,{className:"w-6 h-6 text-white"})}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-orange-500 text-lg font-bold",children:"EX"}),(0,a.jsx)("span",{className:"text-[#005071] text-lg font-bold",children:"Learn"})]})]}),(0,a.jsx)("div",{className:"flex-grow flex justify-center mx-4",children:(0,a.jsx)("div",{className:"relative w-full max-w-xs"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(v.A,{}),(0,a.jsx)("div",{className:"relative",ref:d,children:o?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(n.$,{variant:"ghost",className:"flex items-center space-x-2 px-2 py-1 rounded-full hover:bg-orange-50",onClick:()=>{l(!s)},children:[(0,a.jsx)("span",{className:"bg-orange-500 text-white rounded-full h-8 w-8 flex items-center justify-center font-medium",children:u(o.first_name,o.last_name)}),(0,a.jsx)("span",{className:"text-gray-700 text-sm font-medium",children:o.first_name}),(0,a.jsx)(b.g,{icon:p.Jt$,className:"text-gray-500 text-xs transition-transform duration-200 ".concat(s?"rotate-180":"")})]}),s&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 animate-fade-in",children:[(0,a.jsx)("div",{className:"px-4 py-3 border-b border-gray-100",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"bg-orange-500 text-white rounded-full h-10 w-10 flex items-center justify-center font-medium",children:u(o.first_name,o.last_name)}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[o.first_name," ",o.last_name]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:o.email})]})]})}),(0,a.jsxs)("div",{className:"py-1",children:[(0,a.jsxs)(n.$,{variant:"ghost",className:"w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-orange-50",onClick:()=>{l(!1)},children:[(0,a.jsx)(b.g,{icon:p.X46,className:"mr-3 text-gray-400"}),"Profile"]}),(0,a.jsxs)(n.$,{variant:"ghost",className:"w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50",onClick:()=>{localStorage.removeItem("token"),sessionStorage.removeItem("token"),localStorage.removeItem("authToken"),sessionStorage.removeItem("authToken"),c(null),m.push("/signin")},children:[(0,a.jsx)(b.g,{icon:p.GxD,className:"mr-3 text-red-500"}),"Log out"]})]})]})]}):(0,a.jsx)(n.$,{asChild:!0,className:"bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg",children:(0,a.jsxs)("a",{href:"/signin",className:"flex items-center space-x-2",children:[(0,a.jsx)(b.g,{icon:p.X46,className:"text-sm"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Sign In"})]})})})]})]})})};function N(e){let{children:t}=e,[s,n]=r.useState(!0),i=(0,j.usePathname)();return["/","/courses","/courses/","/signin","/signin/","/signup","/signup/","/forgot-password","/forgot-password/","/my-dashboard","/my-dashboard/","/my-learning","/my-learning/","/my-certificates","/my-certificates/"].includes(i)||i.startsWith("/courses/")&&i.match(/^\/courses\/\d+(\/.*)?$/)?(0,a.jsx)(g.p,{children:t}):(0,a.jsxs)("div",{className:"flex min-h-screen",children:[(0,a.jsx)(f,{sidebarExpanded:s,setSidebarExpanded:n}),(0,a.jsx)("div",{className:"flex flex-col flex-1 min-h-screen",children:(0,a.jsxs)(g.p,{children:[(0,a.jsx)(y,{sidebarExpanded:s}),(0,a.jsx)("main",{className:"flex-1",children:t})]})})]})}},7662:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var a=s(5155),r=s(2115),n=s(7489),i=s(3578),l=s(7470);let o=()=>{let[e,t]=(0,r.useState)(!1),s=(0,r.useRef)(null),{notifications:o,unreadCount:c,markAsRead:d,markAllAsRead:m,clearNotifications:x}=(0,l.E)();(0,r.useEffect)(()=>{let e=e=>{s.current&&!s.current.contains(e.target)&&t(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);let u=(e,t)=>{d(e),window.location.href="/courses/".concat(t)},h=e=>{let t=new Date,s=new Date(e),a=Math.floor((t.getTime()-s.getTime())/6e4);return a<1?"Just now":a<60?"".concat(a,"m ago"):a<1440?"".concat(Math.floor(a/60),"h ago"):"".concat(Math.floor(a/1440),"d ago")};return(0,a.jsxs)("div",{className:"relative",ref:s,children:[(0,a.jsxs)("div",{className:"relative cursor-pointer hover:bg-gray-100 rounded-full p-2 transition-colors duration-200",onClick:()=>{t(!e)},children:[(0,a.jsx)(n.g,{icon:i.z$e,className:"text-gray-600 text-xl"}),c>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium",children:c>99?"99+":c})]}),e&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden",children:[(0,a.jsxs)("div",{className:"px-4 py-3 border-b border-gray-100 flex justify-between items-center",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Notifications"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[c>0&&(0,a.jsxs)("button",{onClick:m,className:"text-xs text-blue-600 hover:text-blue-800 flex items-center",title:"Mark all as read",children:[(0,a.jsx)(n.g,{icon:i.e68,className:"mr-1"}),"Mark all read"]}),(0,a.jsx)("button",{onClick:x,className:"text-xs text-gray-500 hover:text-gray-700",title:"Clear all",children:(0,a.jsx)(n.g,{icon:i.GRI})})]})]}),(0,a.jsx)("div",{className:"max-h-80 overflow-y-auto",children:0===o.length?(0,a.jsxs)("div",{className:"px-4 py-8 text-center text-gray-500",children:[(0,a.jsx)(n.g,{icon:i.z$e,className:"text-3xl mb-2 opacity-50"}),(0,a.jsx)("p",{children:"No notifications yet"})]}):o.map(e=>(0,a.jsx)("div",{className:"px-4 py-3 border-b border-gray-50 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ".concat(e.read?"":"bg-blue-50 border-l-4 border-l-blue-500"),onClick:()=>u(e.id,e.courseId),children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"bg-orange-500 text-white rounded-full h-8 w-8 flex items-center justify-center",children:(0,a.jsx)(n.g,{icon:i.eST,className:"text-sm"})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.message}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,a.jsx)("span",{className:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded",children:e.domain}),(0,a.jsx)("span",{className:"text-xs text-gray-400",children:h(e.timestamp)})]})]}),!e.read&&(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"})})]})},e.id))}),o.length>0&&(0,a.jsx)("div",{className:"px-4 py-2 border-t border-gray-100 text-center",children:(0,a.jsx)("button",{className:"text-sm text-blue-600 hover:text-blue-800 font-medium",children:"View All Notifications"})})]})]})}},7735:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var a=s(2596),r=s(9688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[76,109,266,298,581,352,441,684,358],()=>t(809)),_N_E=e.O()}]);