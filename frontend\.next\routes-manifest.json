{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [], "headers": [], "dynamicRoutes": [{"page": "/courses/[id]", "regex": "^/courses/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/courses/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/courses/[id]/discussion", "regex": "^/courses/([^/]+?)/discussion(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/courses/(?<nxtPid>[^/]+?)/discussion(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/admin", "regex": "^/admin(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin(?:/)?$"}, {"page": "/admin/ai-summarization", "regex": "^/admin/ai\\-summarization(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/ai\\-summarization(?:/)?$"}, {"page": "/admin/avatar-video", "regex": "^/admin/avatar\\-video(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/avatar\\-video(?:/)?$"}, {"page": "/admin/course-builder", "regex": "^/admin/course\\-builder(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/course\\-builder(?:/)?$"}, {"page": "/admin/upload", "regex": "^/admin/upload(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/upload(?:/)?$"}, {"page": "/courses", "regex": "^/courses(?:/)?$", "routeKeys": {}, "namedRegex": "^/courses(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/my-certificates", "regex": "^/my\\-certificates(?:/)?$", "routeKeys": {}, "namedRegex": "^/my\\-certificates(?:/)?$"}, {"page": "/my-dashboard", "regex": "^/my\\-dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/my\\-dashboard(?:/)?$"}, {"page": "/my-learning", "regex": "^/my\\-learning(?:/)?$", "routeKeys": {}, "namedRegex": "^/my\\-learning(?:/)?$"}, {"page": "/signin", "regex": "^/signin(?:/)?$", "routeKeys": {}, "namedRegex": "^/signin(?:/)?$"}, {"page": "/signup", "regex": "^/signup(?:/)?$", "routeKeys": {}, "namedRegex": "^/signup(?:/)?$"}, {"page": "/test-notifications", "regex": "^/test\\-notifications(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-notifications(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}