"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";

export default function SignIn() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    try {
      const res = await fetch(process.env.NEXT_PUBLIC_END_POINT+"/api/login", 
       // https://agenticairi-app6.exlservice.com/AITrainerbackend
        {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password }),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data.error || "Login failed");
      // Store token (optional: localStorage/sessionStorage)
      localStorage.setItem("token", data.token);
      // Redirect based on role
      if (data.user.role === "admin") {
        router.push("/admin");
      } else {
        router.push("/courses");
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-[#fafbfc]">
      <div className="flex flex-col items-center mb-8">
        <div className="bg-orange-500 rounded-lg p-3 mb-4">
          <svg width="32" height="32" fill="white" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/></svg>
        </div>
        <h1 className="text-3xl font-bold mb-2">Welcome back</h1>
        <p className="text-gray-500">Sign in to your AI Trainer account</p>
      </div>
      <form onSubmit={handleSubmit} className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
        <h2 className="text-xl font-semibold mb-2">Sign In</h2>
        <p className="text-gray-500 mb-4">Enter your email and password to access your account</p>
        <div className="mb-4">
          <label className="block mb-1 font-medium">Email</label>
          <input type="email" className="w-full border rounded px-3 py-2" value={email} onChange={e => setEmail(e.target.value)} required placeholder="Enter your email" />
        </div>
        <div className="mb-4">
          <label className="block mb-1 font-medium">Password</label>
          <input type="password" className="w-full border rounded px-3 py-2" value={password} onChange={e => setPassword(e.target.value)} required placeholder="Enter your password" />
        </div>
        {error && <div className="text-red-500 mb-2">{error}</div>}
        <button type="submit" className="w-full bg-orange-500 text-white py-2 rounded mt-2 hover:bg-orange-600 transition" disabled={loading}>{loading ? "Signing In..." : "Sign In"}</button>
        <div className="text-center mt-4 text-gray-600">
          Don't have an account? <a href="/signup" className="text-orange-500 hover:underline">Sign up</a>
        </div>
      </form>
    </div>
  );
} 