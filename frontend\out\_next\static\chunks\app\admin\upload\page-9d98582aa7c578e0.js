(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[798],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>o});var a=r(5155);r(2115);var s=r(9708),l=r(2085),i=r(9434);let n=(0,l.F)("cursor-pointer inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"underline-offset-4 hover:underline text-primary",brand:"bg-brand-blue text-white border border-brand-blue hover:bg-brand-blue/90 hover:border-brand-blue/80",sucess:"bg-green-600 text-white border border-green-700 hover:bg-green-600/90 hover:border-green-600/80",warning:"bg-amber-400 text-gray-900 border border-yellow-500 hover:bg-yellow-400/90 hover:border-yellow-400/80"},size:{default:"h-10 py-2 px-4",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-md",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:l,asChild:o=!1,...d}=e,c=o?s.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(n({variant:r,size:l,className:t})),...d})}},4416:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4786:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var a=r(5155),s=r(2115);let l=(0,r(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var i=r(7434),n=r(4416),o=r(285),d=r(9434);function c(e){let{className:t,type:r,...s}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,d.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}function u(e){let{className:t,...r}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,d.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...r})}var p=r(5057),m=r(9409);let g=e=>{let{files:t=[],onFilesChange:r,onFileAdd:d,onFileRemove:c,onFileProgress:u,maxFiles:p=10,maxFileSize:m=0xa00000,acceptedFileTypes:g=[".pdf",".doc",".docx",".txt",".jpg",".jpeg",".png"],multiple:x=!0,title:h="Upload Files",subtitle:f="Upload documents you want to share with your team",dragText:b="Drag and drop here",browseButtonText:v="Browse files",showProgress:y=!0,showFileList:j=!0,colors:w=["blue","green","yellow","red","purple"],className:N="",onUploadStart:k,onUploadComplete:z,onUploadError:F,uploadFunction:C}=e,[M,_]=(0,s.useState)([]),[D,A]=(0,s.useState)(!1),P=(0,s.useRef)(null),S=r?t:M,E=e=>{var t;if(e.size>m)return"File size must be less than ".concat(Math.round(m/1024/1024),"MB");let r="."+(null==(t=e.name.split(".").pop())?void 0:t.toLowerCase());return g.includes(r)?S.length>=p?"Maximum ".concat(p," files allowed"):null:"File type not supported. Accepted types: ".concat(g.join(", "))},U=()=>"file_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),B=()=>w[Math.floor(Math.random()*w.length)],q=(0,s.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?A(!0):"dragleave"===e.type&&A(!1)},[]),T=(0,s.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),A(!1),e.dataTransfer.files&&e.dataTransfer.files[0]&&J(e.dataTransfer.files)},[S.length,p]),I=(0,s.useCallback)(e=>{e.preventDefault(),e.target.files&&e.target.files[0]&&J(e.target.files)},[S.length,p]),J=(0,s.useCallback)(e=>{let t=[],a=[];if(Array.from(e).forEach(e=>{let r=E(e);r?a.push("".concat(e.name,": ").concat(r)):t.push(e)}),a.length>0&&(console.error("File validation errors:",a),alert(a.join("\n"))),0===t.length)return;if(d)return void d(t);let s=t.map(e=>({id:U(),name:e.name,progress:0,status:"uploading",color:B(),file:e,size:e.size,type:e.type}));(r||_)(e=>[...e,...s]),s.forEach(e=>{k&&k(e.file),C?C(e.file,t=>{L(e.id,t)}).then(()=>{O(e.id,"completed"),z&&z(e)}).catch(t=>{O(e.id,"error"),F&&F(e,t.message)}):H(e.id)})},[S,p,m,g,d,k,C,r,_]),H=e=>{let t=0,r=setInterval(()=>{(t+=30*Math.random())>=100&&(t=100,clearInterval(r),O(e,"completed")),L(e,Math.floor(t))},500)},L=(e,t)=>{let a=r||_;u&&u(e,t),a(r=>r.map(r=>r.id===e?{...r,progress:t}:r))},O=(e,t)=>{(r||_)(r=>r.map(r=>r.id===e?{...r,status:t,progress:"completed"===t?100:r.progress}:r))},V=(0,s.useCallback)(e=>{let t=r||_;c?c(e):t(t=>t.filter(t=>t.id!==e))},[c,r,_]),R=e=>({blue:"bg-blue-500",green:"bg-green-500",yellow:"bg-yellow-500",red:"bg-red-500",purple:"bg-purple-500"})[e],$=e=>({blue:"text-blue-500",green:"text-green-500",yellow:"text-yellow-500",red:"text-red-500",purple:"text-purple-500"})[e],Z=e=>{switch(e){case"completed":return"text-green-600";case"error":return"text-red-600";default:return"text-gray-500"}},G=e=>{switch(e){case"completed":return"Completed";case"error":return"Error";default:return"Uploading..."}},K=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]};return(0,a.jsx)("div",{className:"max-w-7xl mx-auto p-6 ".concat(N),children:(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg p-6",children:[(0,a.jsx)("div",{className:"mb-6"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 items-start",children:[(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 ".concat(D?"border-[#005071] bg-blue-50":"border-gray-300 hover:border-[#005071] hover:bg-gray-50"),onDragEnter:q,onDragLeave:q,onDragOver:q,onDrop:T,children:[(0,a.jsx)("div",{className:"mb-4",children:(0,a.jsx)(l,{className:"mx-auto h-12 w-12 text-gray-400"})}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-700 mb-1",children:b}),(0,a.jsx)("p",{className:"text-gray-500 text-sm mb-4",children:"-OR-"}),(0,a.jsx)(o.$,{onClick:()=>{var e;null==(e=P.current)||e.click()},className:"bg-[#005071] hover:bg-[#005071]/[90%] text-white px-6 py-2.5 rounded-lg font-medium transition-colors duration-200",children:v}),(0,a.jsx)("input",{ref:P,type:"file",multiple:x,onChange:I,className:"hidden",accept:g.join(",")})]})}),j&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["Uploaded Files (",S.length,")"]})}),(0,a.jsxs)("div",{className:"max-h-96 overflow-y-auto space-y-3 pr-2",children:[S.map(e=>(0,a.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-3 hover:shadow-sm transition-shadow",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 flex-1 min-w-0",children:[(0,a.jsx)(i.A,{className:"h-4 w-4 ".concat($(e.color)," flex-shrink-0")}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"font-medium text-gray-700 truncate text-sm",children:e.name}),e.size&&(0,a.jsx)("p",{className:"text-xs text-gray-500",children:K(e.size)})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 flex-shrink-0",children:[y&&(0,a.jsxs)("span",{className:"text-xs ".concat($(e.color)),children:[e.progress,"%"]}),(0,a.jsx)("span",{className:"text-xs ".concat(Z(e.status)),children:G(e.status)}),(0,a.jsx)("button",{onClick:()=>V(e.id),className:"text-gray-400 hover:text-red-500 transition-colors p-1","aria-label":"Remove ".concat(e.name),children:(0,a.jsx)(n.A,{className:"h-3 w-3"})})]})]}),y&&(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-1.5",children:(0,a.jsx)("div",{className:"h-1.5 rounded-full transition-all duration-300 ".concat("error"===e.status?"bg-red-500":R(e.color)),style:{width:"".concat(e.progress,"%")}})})]},e.id)),0===S.length&&(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200",children:[(0,a.jsx)(i.A,{className:"mx-auto h-8 w-8 text-gray-300 mb-2"}),(0,a.jsx)("p",{className:"text-sm",children:"No files uploaded yet"})]})]})]})]})]})})};function x(){let[e,t]=(0,s.useState)({title:"",description:"",domain:"",level:"Beginner",estimated_hours:""}),[r,l]=(0,s.useState)([]),[i,n]=(0,s.useState)(!1),[d,x]=(0,s.useState)(""),h=(e,r)=>{t(t=>({...t,[e]:r}))},f=async(e,t)=>new Promise((e,r)=>{let a=0,s=setInterval(()=>{t(a+=20),a>=100&&(clearInterval(s),e())},500)}),b=async a=>{a.preventDefault(),n(!0),x("");try{if(0===r.length){x("Please upload at least one PDF file"),n(!1);return}if(r.filter(e=>"completed"!==e.status).length>0){x("Please wait for all files to finish uploading"),n(!1);return}let a=new FormData;if(a.append("title",e.title),a.append("description",e.description),a.append("domain",e.domain),a.append("level",e.level),a.append("estimated_hours",e.estimated_hours),r.forEach((e,t)=>{e.file&&a.append("file",e.file)}),console.log("Submitting form with data:",{...e,files:r.map(e=>({id:e.id,name:e.name,size:e.size,status:e.status}))}),(await fetch("/api/courses",{method:"POST",body:a})).ok)x("Course uploaded successfully!"),t({title:"",description:"",domain:"",level:"Beginner",estimated_hours:""}),l([]);else throw Error("Upload failed")}catch(e){console.error("Upload error:",e),x("Upload failed. Please try again.")}finally{n(!1)}};return(0,a.jsxs)("div",{className:"max-w-5xl mx-auto bg-white rounded-2xl shadow-lg p-6 mt-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-orange-600 mb-6 text-center",children:"Upload New Course"}),(0,a.jsxs)("form",{onSubmit:b,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"title",children:"Course Title"}),(0,a.jsx)(c,{id:"title",type:"text",value:e.title,onChange:e=>h("title",e.target.value),placeholder:"Enter course title",required:!0,className:"focus:ring-2 focus:ring-orange-200"})]}),(0,a.jsxs)("div",{className:"col-span-2 space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"description",children:"Description"}),(0,a.jsx)(u,{id:"description",value:e.description,onChange:e=>h("description",e.target.value),placeholder:"Enter course description",rows:3,required:!0,className:"focus:ring-2 focus:ring-orange-200 resize-none"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"domain",children:"Domain"}),(0,a.jsxs)(m.l6,{value:e.domain,onValueChange:e=>h("domain",e),children:[(0,a.jsx)(m.bq,{className:"w-full focus:ring-2 focus:ring-orange-200",children:(0,a.jsx)(m.yv,{placeholder:"Select Domain"})}),(0,a.jsxs)(m.gC,{children:[(0,a.jsx)(m.eb,{value:"Medical",children:"Medicine"}),(0,a.jsx)(m.eb,{value:"Engineering",children:"Engineering"}),(0,a.jsx)(m.eb,{value:"Science",children:"Science"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{children:"Level"}),(0,a.jsxs)(m.l6,{value:e.level,onValueChange:e=>h("level",e),children:[(0,a.jsx)(m.bq,{className:"w-full focus:ring-2 focus:ring-orange-200",children:(0,a.jsx)(m.yv,{placeholder:"Select level"})}),(0,a.jsxs)(m.gC,{children:[(0,a.jsx)(m.eb,{value:"Beginner",children:"Beginner"}),(0,a.jsx)(m.eb,{value:"Intermediate",children:"Intermediate"}),(0,a.jsx)(m.eb,{value:"Advanced",children:"Advanced"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(p.J,{htmlFor:"estimated_hours",children:"Estimated Hours"}),(0,a.jsx)(c,{id:"estimated_hours",type:"number",min:0,max:100,value:e.estimated_hours,onChange:e=>h("estimated_hours",e.target.value),placeholder:"0"})]})]}),(0,a.jsxs)("div",{className:"col-span-full space-y-2",children:[(0,a.jsx)(p.J,{children:"Upload PDF File"}),(0,a.jsx)(g,{files:r,onFilesChange:e=>{console.log("Files updated:",e),l(e)},onFileRemove:e=>{console.log("File removed:",e),l(t=>t.filter(t=>t.id!==e))},uploadFunction:f,title:"Upload Course Material",subtitle:"Upload your course PDF file",maxFiles:1,maxFileSize:0x3200000,acceptedFileTypes:[".pdf"],dragText:"Drag and drop your PDF here",browseButtonText:"Browse PDF Files",className:""})]}),(0,a.jsx)("div",{className:"flex justify-center pt-4",children:(0,a.jsx)(o.$,{type:"submit",disabled:i||!(""!==e.title.trim()&&""!==e.description.trim()&&""!==e.domain.trim()&&""!==e.estimated_hours.trim()&&r.length>0&&r.every(e=>"completed"===e.status)),className:"bg-orange-500 hover:bg-orange-600 text-white px-8 py-3 rounded-lg text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed",children:i?(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),(0,a.jsx)("span",{children:"Uploading..."})]}):"Upload Course ".concat(r.length>0?"(".concat(r.length," file").concat(r.length>1?"s":"",")"):"")})}),d&&(0,a.jsx)("div",{className:"text-center font-medium mt-4 p-3 rounded-lg ".concat(d.includes("success")||d.includes("uploaded")?"text-green-600 bg-green-50":d.includes("failed")||d.includes("error")?"text-red-600 bg-red-50":"text-blue-600 bg-blue-50"),children:d})]})]})}},5057:(e,t,r)=>{"use strict";r.d(t,{J:()=>i});var a=r(5155);r(2115);var s=r(968),l=r(9434);function i(e){let{className:t,...r}=e;return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},6347:(e,t,r)=>{Promise.resolve().then(r.bind(r,4786))},7434:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},9409:(e,t,r)=>{"use strict";r.d(t,{bq:()=>u,eb:()=>m,gC:()=>p,l6:()=>d,yv:()=>c});var a=r(5155);r(2115);var s=r(9963),l=r(6474),i=r(5196),n=r(7863),o=r(9434);function d(e){let{...t}=e;return(0,a.jsx)(s.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,a.jsx)(s.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:r="default",children:i,...n}=e;return(0,a.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":r,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n,children:[i,(0,a.jsx)(s.In,{asChild:!0,children:(0,a.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function p(e){let{className:t,children:r,position:l="popper",...i}=e;return(0,a.jsx)(s.ZL,{children:(0,a.jsxs)(s.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:l,...i,children:[(0,a.jsx)(g,{}),(0,a.jsx)(s.LM,{className:(0,o.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:r}),(0,a.jsx)(x,{})]})})}function m(e){let{className:t,children:r,...l}=e;return(0,a.jsxs)(s.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...l,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(s.VF,{children:(0,a.jsx)(i.A,{className:"size-4"})})}),(0,a.jsx)(s.p4,{children:r})]})}function g(e){let{className:t,...r}=e;return(0,a.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(n.A,{className:"size-4"})})}function x(e){let{className:t,...r}=e;return(0,a.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(l.A,{className:"size-4"})})}},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>l});var a=r(2596),s=r(9688);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[352,578,441,684,358],()=>t(6347)),_N_E=e.O()}]);