"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[494],{5494:(e,t,s)=>{s.d(t,{default:()=>c});var r=s(5155),a=s(2115),n=s(7489),i=s(3578),l=s(7662),o=s(7949);let c=()=>{let[e,t]=(0,a.useState)(!1),[s,c]=(0,a.useState)(null),d=(0,a.useRef)(null),x=e=>{try{let t=e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),s=decodeURIComponent(atob(t).split("").map(function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(s)}catch(e){return console.error("Error decoding JWT:",e),null}};(0,a.useEffect)(()=>{let e=localStorage.getItem("token");if(e){let t=x(e);t&&c({id:t.id,email:t.email,username:t.username,role:t.role,first_name:t.first_name||t.username,last_name:t.last_name||""})}},[]);let m=(e,t)=>(e?e.charAt(0).toUpperCase():"")+(t?t.charAt(0).toUpperCase():"")||"U";return(0,a.useEffect)(()=>{let e=e=>{d.current&&!d.current.contains(e.target)&&t(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,r.jsxs)("nav",{className:"bg-white p-4 shadow-md",children:[(0,r.jsxs)("div",{className:"container mx-auto flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"bg-orange-500 rounded-xl p-2 flex items-center justify-center rounded-lg",children:(0,r.jsx)(o.A,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-orange-500 text-lg font-bold",children:"EX"}),(0,r.jsx)("span",{className:"text-[#005071] text-lg font-bold",children:"Learn"})]})]}),(0,r.jsx)("div",{className:"flex-grow flex justify-center mx-4",children:(0,r.jsx)("div",{className:"relative w-full max-w-[280px]",children:(0,r.jsx)("input",{type:"text",placeholder:"Search courses, topics, or instructors...",className:"w-full pl-3 pr-3 py-1 rounded-full border border-transparent focus:outline-none focus:ring-1 focus:ring-orange-100 text-xs placeholder-gray-400"})})}),(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,r.jsx)(l.A,{}),(0,r.jsx)("div",{className:"relative",ref:d,children:s?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 cursor-pointer hover:bg-gray-50 rounded-lg px-2 py-1 transition-colors duration-200",onClick:()=>{t(!e)},children:[(0,r.jsx)("div",{className:"bg-orange-500 text-white rounded-full h-8 w-8 flex items-center justify-center font-medium",children:m(s.first_name,s.last_name)}),(0,r.jsx)("span",{className:"text-gray-700 text-sm font-medium",children:s.first_name}),(0,r.jsx)(n.g,{icon:i.Jt$,className:"text-gray-500 text-xs transition-transform duration-200 ".concat(e?"rotate-180":"")})]}),e&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50",children:[(0,r.jsx)("div",{className:"px-4 py-3 border-b border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"bg-orange-500 text-white rounded-full h-10 w-10 flex items-center justify-center font-medium",children:m(s.first_name,s.last_name)}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[s.first_name," ",s.last_name]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:s.email})]})]})}),(0,r.jsxs)("div",{className:"py-1",children:[(0,r.jsxs)("button",{className:"w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200",onClick:()=>{t(!1)},children:[(0,r.jsx)(n.g,{icon:i.X46,className:"mr-3 text-gray-400"}),"Profile"]}),(0,r.jsxs)("button",{className:"w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200",onClick:()=>{localStorage.removeItem("token"),sessionStorage.removeItem("token"),localStorage.removeItem("authToken"),sessionStorage.removeItem("authToken"),c(null),window.location.href="/signin"},children:[(0,r.jsx)(n.g,{icon:i.GxD,className:"mr-3 text-red-500"}),"Log out"]})]})]})]}):(0,r.jsxs)("a",{href:"/signin",className:"flex items-center space-x-2 bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors duration-200",children:[(0,r.jsx)(n.g,{icon:i.X46,className:"text-sm"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Sign In"})]})})]})]}),(0,r.jsx)("div",{className:"container mx-auto mt-4",children:(0,r.jsxs)("ul",{className:"flex space-x-8",children:[(0,r.jsx)("li",{children:(0,r.jsxs)("a",{href:"/",className:"flex items-center space-x-2 text-orange-500 border-b-2 border-orange-500 pb-2",children:[(0,r.jsx)(n.g,{icon:i.v02,className:"text-sm"}),(0,r.jsx)("span",{children:"Home"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)("a",{href:"/my-dashboard",className:"flex items-center space-x-2 text-gray-700 hover:text-orange-500",children:[(0,r.jsx)(n.g,{icon:i.xiI,className:"text-sm"}),(0,r.jsx)("span",{children:"My Dashboard"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)("a",{href:"/my-learning",className:"flex items-center space-x-2 text-gray-700 hover:text-orange-500",children:[(0,r.jsx)(n.g,{icon:i.ReK,className:"text-sm"}),(0,r.jsx)("span",{children:"My Learning"})]})}),(0,r.jsx)("li",{children:(0,r.jsxs)("a",{href:"/my-certificates",className:"flex items-center space-x-2 text-gray-700 hover:text-orange-500",children:[(0,r.jsx)(n.g,{icon:i.fmL,className:"text-sm"}),(0,r.jsx)("span",{children:"Certificates"})]})})]})})]})}},7470:(e,t,s)=>{s.d(t,{E:()=>o,p:()=>l});var r=s(5155),a=s(2115),n=s(4298);let i=(0,a.createContext)(void 0),l=e=>{let{children:t}=e,[s,l]=(0,a.useState)([]),[o,c]=(0,a.useState)(!1),[d,x]=(0,a.useState)(null);(0,a.useEffect)(()=>{let e=(0,n.io)("https://agenticairi-app6.exlservice.com/AITrainerbackend",{transports:["websocket","polling"],autoConnect:!0});return x(e),e.on("connect",()=>{console.log("Connected to notification server"),c(!0);let t=localStorage.getItem("token");if(t)try{let s=JSON.parse(atob(t.split(".")[1]));e.emit("join_user",s.id)}catch(e){console.error("Error decoding token:",e)}}),e.on("disconnect",()=>{console.log("Disconnected from notification server"),c(!1)}),e.on("new_notification",e=>{console.log("New notification received:",e),l(t=>[e,...t]),"granted"===Notification.permission&&new Notification(e.title,{body:e.message,icon:"/favicon.ico"})}),"default"===Notification.permission&&Notification.requestPermission(),m(),()=>{e.close()}},[]);let m=async()=>{try{let e=localStorage.getItem("token");if(!e)return;let t=await fetch("https://agenticairi-app6.exlservice.com/AITrainerbackend/api/notifications",{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}});if(t.ok){let e=await t.json();l(e)}}catch(e){console.error("Error fetching notifications:",e)}},u=s.filter(e=>!e.read).length;return(0,r.jsx)(i.Provider,{value:{notifications:s,unreadCount:u,isConnected:o,markAsRead:e=>{l(t=>t.map(t=>t.id===e?{...t,read:!0}:t))},markAllAsRead:()=>{l(e=>e.map(e=>({...e,read:!0})))},clearNotifications:()=>{l([])}},children:t})},o=()=>{let e=(0,a.useContext)(i);if(void 0===e)throw Error("useNotifications must be used within a NotificationProvider");return e}},7662:(e,t,s)=>{s.d(t,{A:()=>o});var r=s(5155),a=s(2115),n=s(7489),i=s(3578),l=s(7470);let o=()=>{let[e,t]=(0,a.useState)(!1),s=(0,a.useRef)(null),{notifications:o,unreadCount:c,markAsRead:d,markAllAsRead:x,clearNotifications:m}=(0,l.E)();(0,a.useEffect)(()=>{let e=e=>{s.current&&!s.current.contains(e.target)&&t(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);let u=(e,t)=>{d(e),window.location.href="/courses/".concat(t)},h=e=>{let t=new Date,s=new Date(e),r=Math.floor((t.getTime()-s.getTime())/6e4);return r<1?"Just now":r<60?"".concat(r,"m ago"):r<1440?"".concat(Math.floor(r/60),"h ago"):"".concat(Math.floor(r/1440),"d ago")};return(0,r.jsxs)("div",{className:"relative",ref:s,children:[(0,r.jsxs)("div",{className:"relative cursor-pointer hover:bg-gray-100 rounded-full p-2 transition-colors duration-200",onClick:()=>{t(!e)},children:[(0,r.jsx)(n.g,{icon:i.z$e,className:"text-gray-600 text-xl"}),c>0&&(0,r.jsx)("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium",children:c>99?"99+":c})]}),e&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden",children:[(0,r.jsxs)("div",{className:"px-4 py-3 border-b border-gray-100 flex justify-between items-center",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Notifications"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[c>0&&(0,r.jsxs)("button",{onClick:x,className:"text-xs text-blue-600 hover:text-blue-800 flex items-center",title:"Mark all as read",children:[(0,r.jsx)(n.g,{icon:i.e68,className:"mr-1"}),"Mark all read"]}),(0,r.jsx)("button",{onClick:m,className:"text-xs text-gray-500 hover:text-gray-700",title:"Clear all",children:(0,r.jsx)(n.g,{icon:i.GRI})})]})]}),(0,r.jsx)("div",{className:"max-h-80 overflow-y-auto",children:0===o.length?(0,r.jsxs)("div",{className:"px-4 py-8 text-center text-gray-500",children:[(0,r.jsx)(n.g,{icon:i.z$e,className:"text-3xl mb-2 opacity-50"}),(0,r.jsx)("p",{children:"No notifications yet"})]}):o.map(e=>(0,r.jsx)("div",{className:"px-4 py-3 border-b border-gray-50 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ".concat(e.read?"":"bg-blue-50 border-l-4 border-l-blue-500"),onClick:()=>u(e.id,e.courseId),children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"bg-orange-500 text-white rounded-full h-8 w-8 flex items-center justify-center",children:(0,r.jsx)(n.g,{icon:i.eST,className:"text-sm"})})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.message}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,r.jsx)("span",{className:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded",children:e.domain}),(0,r.jsx)("span",{className:"text-xs text-gray-400",children:h(e.timestamp)})]})]}),!e.read&&(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"})})]})},e.id))}),o.length>0&&(0,r.jsx)("div",{className:"px-4 py-2 border-t border-gray-100 text-center",children:(0,r.jsx)("button",{className:"text-sm text-blue-600 hover:text-blue-800 font-medium",children:"View All Notifications"})})]})]})}}}]);