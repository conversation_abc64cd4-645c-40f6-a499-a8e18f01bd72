(()=>{var e={};e.id=879,e.ids=[879],e.modules={440:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1820:e=>{"use strict";e.exports=require("os")},2386:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=s(5239),a=s(8088),n=s(8170),i=s.n(n),o=s(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(r,l);let d={children:["",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8255)),"C:\\Projects\\AI-Trainer\\frontend\\src\\app\\signup\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,8429)),"C:\\Projects\\AI-Trainer\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\AI-Trainer\\frontend\\src\\app\\signup\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/signup/page",pathname:"/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3221:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>i});var t=s(687),a=s(3210),n=s(6189);function i(){let[e,r]=(0,a.useState)({first_name:"",last_name:"",username:"",email:"",password:"",confirmPassword:""}),[s,i]=(0,a.useState)(""),[o,l]=(0,a.useState)(!1),d=(0,n.useRouter)(),c=s=>{r({...e,[s.target.name]:s.target.value})},u=async r=>{if(r.preventDefault(),i(""),e.password!==e.confirmPassword)return void i("Passwords do not match");l(!0);try{let r=await fetch("https://agenticairi-app6.exlservice.com/AITrainerbackend/api/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({first_name:e.first_name,last_name:e.last_name,username:e.username,email:e.email,password:e.password})}),s=await r.json();if(!r.ok)throw Error(s.error||"Registration failed");d.push("/signin")}catch(e){i(e.message)}finally{l(!1)}};return(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-[#fafbfc]",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center mb-8",children:[(0,t.jsx)("div",{className:"bg-orange-500 rounded-lg p-3 mb-4",children:(0,t.jsx)("svg",{width:"32",height:"32",fill:"white",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"})})}),(0,t.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"Create your account"}),(0,t.jsx)("p",{className:"text-gray-500",children:"Join the AI Trainer platform"})]}),(0,t.jsxs)("form",{onSubmit:u,className:"bg-white p-8 rounded-lg shadow-md w-full max-w-md",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Sign Up"}),(0,t.jsx)("p",{className:"text-gray-500 mb-4",children:"Create your account to start learning or teaching"}),(0,t.jsxs)("div",{className:"mb-4 flex gap-2",children:[(0,t.jsxs)("div",{className:"w-1/2",children:[(0,t.jsx)("label",{className:"block mb-1 font-medium",children:"First Name"}),(0,t.jsx)("input",{type:"text",name:"first_name",className:"w-full border rounded px-3 py-2",value:e.first_name,onChange:c,required:!0,placeholder:"John"})]}),(0,t.jsxs)("div",{className:"w-1/2",children:[(0,t.jsx)("label",{className:"block mb-1 font-medium",children:"Last Name"}),(0,t.jsx)("input",{type:"text",name:"last_name",className:"w-full border rounded px-3 py-2",value:e.last_name,onChange:c,required:!0,placeholder:"Doe"})]})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block mb-1 font-medium",children:"Username"}),(0,t.jsx)("input",{type:"text",name:"username",className:"w-full border rounded px-3 py-2",value:e.username,onChange:c,required:!0,placeholder:"johndoe"})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block mb-1 font-medium",children:"Email"}),(0,t.jsx)("input",{type:"email",name:"email",className:"w-full border rounded px-3 py-2",value:e.email,onChange:c,required:!0,placeholder:"<EMAIL>"})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block mb-1 font-medium",children:"I want to"}),(0,t.jsx)("select",{className:"w-full border rounded px-3 py-2 bg-white",disabled:!0,children:(0,t.jsx)("option",{children:"Learn (take courses)"})})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block mb-1 font-medium",children:"Password"}),(0,t.jsx)("input",{type:"password",name:"password",className:"w-full border rounded px-3 py-2",value:e.password,onChange:c,required:!0,placeholder:"Enter your password"})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block mb-1 font-medium",children:"Confirm Password"}),(0,t.jsx)("input",{type:"password",name:"confirmPassword",className:"w-full border rounded px-3 py-2",value:e.confirmPassword,onChange:c,required:!0,placeholder:"Confirm your password"})]}),s&&(0,t.jsx)("div",{className:"text-red-500 mb-2",children:s}),(0,t.jsx)("button",{type:"submit",className:"w-full bg-orange-500 text-white py-2 rounded mt-2 hover:bg-orange-600 transition",disabled:o,children:o?"Creating Account...":"Create Account"}),(0,t.jsxs)("div",{className:"text-center mt-4 text-gray-600",children:["Already have an account? ",(0,t.jsx)("a",{href:"/signin",className:"text-orange-500 hover:underline",children:"Sign in"})]})]})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3789:(e,r,s)=>{Promise.resolve().then(s.bind(s,3221))},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},7910:e=>{"use strict";e.exports=require("stream")},8255:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\AI-Trainer\\\\frontend\\\\src\\\\app\\\\signup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\AI-Trainer\\frontend\\src\\app\\signup\\page.tsx","default")},8354:e=>{"use strict";e.exports=require("util")},8525:(e,r,s)=>{Promise.resolve().then(s.bind(s,8255))},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9551:e=>{"use strict";e.exports=require("url")},9646:e=>{"use strict";e.exports=require("child_process")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,773,658,114],()=>s(2386));module.exports=t})();