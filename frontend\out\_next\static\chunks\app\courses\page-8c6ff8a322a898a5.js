(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[295],{2309:(e,s,t)=>{Promise.resolve().then(t.bind(t,6685))},6685:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var a=t(5155),l=t(5494),r=t(2115),i=t(7489),n=t(3578);let c=e=>{let{course:s}=e;return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200 group",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"w-full h-40 bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-2",children:(0,a.jsx)(i.g,{icon:n.eST,className:"text-white text-2xl"})}),(0,a.jsx)("p",{className:"text-blue-700 text-sm font-medium",children:s.domain})]})}),(0,a.jsx)("div",{className:"absolute inset-0 bg-blue-500 bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200 flex items-center justify-center",children:(0,a.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:(0,a.jsx)("div",{className:"bg-white rounded-full p-3 shadow-lg",children:(0,a.jsx)(i.g,{icon:n.ijD,className:"text-blue-500 text-xl"})})})}),(0,a.jsx)("span",{className:"absolute top-3 left-3 px-2 py-1 rounded-full text-xs font-medium ".concat((e=>{switch(e.toLowerCase()){case"beginner":return"bg-green-100 text-green-700";case"intermediate":return"bg-yellow-100 text-yellow-700";case"advanced":return"bg-red-100 text-red-700";default:return"bg-gray-100 text-gray-700"}})(s.level)),children:s.level})]}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2 line-clamp-2",children:s.title}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-3 line-clamp-2",children:s.description}),(0,a.jsx)("div",{className:"flex items-center justify-between text-xs text-gray-500 mb-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(i.g,{icon:n.a$,className:"mr-1"}),s.estimated_hours,"h"]}),(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(i.g,{icon:n.ReK,className:"mr-1"}),"Course"]})]})}),(0,a.jsxs)("a",{href:"/courses/".concat(s.id),className:"w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 transition-colors duration-200 text-sm font-medium flex items-center justify-center group",children:["View Course",(0,a.jsx)(i.g,{icon:n.XkK,className:"ml-2 text-xs group-hover:translate-x-1 transition-transform duration-200"})]})]})]})};function d(){let[e,s]=(0,r.useState)({}),[t,d]=(0,r.useState)(!0),[o,x]=(0,r.useState)(null),[m,u]=(0,r.useState)("");(0,r.useEffect)(()=>{(async()=>{try{d(!0);let e=await fetch("http://localhost:5001/api/courses/by-domain");if(!e.ok)throw Error("Failed to fetch courses");let t=await e.json();s(t)}catch(e){x(e instanceof Error?e.message:"An error occurred")}finally{d(!1)}})()},[]);let h=e=>({"Healthcare & Medicine":"Medical education, healthcare training, and clinical skills","Insurance & Risk Management":"Insurance policies, risk assessment, and compliance",Science:"Scientific research, laboratory skills, and theoretical knowledge",Technology:"Programming, software development, and technical skills",Engineering:"Engineering principles, design, and problem-solving",Mathematics:"Mathematical concepts, statistics, and analytical thinking","Web Development":"Frontend, backend, and full-stack development","Data Science":"Data analysis, machine learning, and statistical modeling",Design:"UI/UX design, graphic design, and creative skills",Business:"Business strategy, management, and entrepreneurship",Finance:"Financial analysis, investment, and economic principles"})[e]||"Comprehensive courses in this field",g=Object.keys(e).filter(s=>{if(!m)return!0;let t=s.toLowerCase().includes(m.toLowerCase()),a=e[s].some(e=>e.title.toLowerCase().includes(m.toLowerCase())||e.description.toLowerCase().includes(m.toLowerCase()));return t||a});return t?(0,a.jsxs)("div",{className:"flex flex-col min-h-screen bg-[#fafbfc]",children:[(0,a.jsx)(l.default,{}),(0,a.jsx)("main",{className:"flex-grow container mx-auto px-4 py-8",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"text-lg text-gray-600",children:"Loading courses..."})})})]}):o?(0,a.jsxs)("div",{className:"flex flex-col min-h-screen bg-[#fafbfc]",children:[(0,a.jsx)(l.default,{}),(0,a.jsx)("main",{className:"flex-grow container mx-auto px-4 py-8",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsxs)("div",{className:"text-lg text-red-600",children:["Error: ",o]})})})]}):(0,a.jsxs)("div",{className:"flex flex-col min-h-screen bg-[#fafbfc]",children:[(0,a.jsx)(l.default,{}),(0,a.jsxs)("main",{className:"flex-grow container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-800 mb-4",children:"All Courses by Industry"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6",children:"Discover courses tailored to your professional field and interests"}),(0,a.jsxs)("div",{className:"max-w-md mx-auto relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(i.g,{icon:n.MjD,className:"text-gray-400"})}),(0,a.jsx)("input",{type:"text",placeholder:"Search courses...",value:m,onChange:e=>u(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"})]})]}),0===g.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(i.g,{icon:n.MjD,className:"text-gray-400 text-4xl mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"No courses found"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Try adjusting your search terms"})]}):g.map(s=>{let t=e[s],l=m?t.filter(e=>e.title.toLowerCase().includes(m.toLowerCase())||e.description.toLowerCase().includes(m.toLowerCase())):t;return 0===l.length?null:(0,a.jsxs)("div",{className:"mb-12",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-2",children:s}),(0,a.jsx)("p",{className:"text-gray-600",children:h(s)})]}),(0,a.jsxs)("div",{className:"text-blue-500 font-medium flex items-center",children:["Total: ",t.length," course",1!==t.length?"s":""]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:l.map(e=>(0,a.jsx)(c,{course:e},e.id))})]},s)}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8 mt-12",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 text-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-blue-500 mb-2",children:Object.values(e).flat().length}),(0,a.jsx)("div",{className:"text-gray-600",children:"Total Courses"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-blue-500 mb-2",children:Object.keys(e).length}),(0,a.jsx)("div",{className:"text-gray-600",children:"Industry Domains"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-blue-500 mb-2",children:Object.values(e).flat().reduce((e,s)=>e+s.estimated_hours,0)}),(0,a.jsx)("div",{className:"text-gray-600",children:"Learning Hours"})]})]})})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[266,298,581,494,441,684,358],()=>s(2309)),_N_E=e.O()}]);