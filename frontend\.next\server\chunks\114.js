exports.id=114,exports.ids=[114],exports.modules={63:(e,t,s)=>{Promise.resolve().then(s.bind(s,3799))},534:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\AI-Trainer\\\\frontend\\\\src\\\\app\\\\ClientLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\AI-Trainer\\frontend\\src\\app\\ClientLayout.tsx","default")},1135:()=>{},1319:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},3799:(e,t,s)=>{"use strict";s.d(t,{default:()=>y});var r=s(687),a=s(3210),n=s.n(a),i=s(9523),o=s(7351),l=s(7033),c=s(4952),d=s(22),m=s(5583),x=s(8869),u=s(9300);let h=[{label:"Course Builder",icon:(0,r.jsx)(d.A,{className:"w-5 h-5"}),href:"/admin/upload"},{label:"AI Summarization",icon:(0,r.jsx)(m.A,{className:"w-5 h-5"}),href:"/admin/ai-summarization"},{label:"Avatar & Video",icon:(0,r.jsx)(x.A,{className:"w-5 h-5"}),href:"/admin/avatar-video"},{label:"Course Builder",icon:(0,r.jsx)(u.A,{className:"w-5 h-5"}),href:"/admin/course-builder"}];function f({sidebarExpanded:e,setSidebarExpanded:t,activePanel:s,setActivePanel:a}){return(0,r.jsxs)("aside",{className:`bg-white border-r border-gray-200 shadow-sm transition-all duration-300 ease-in-out flex flex-col ${e?"w-48":"w-16"}`,children:[(0,r.jsxs)("div",{className:`py-2 px-4 border-b border-gray-200 flex items-center ${e?"justify-between":"justify-center"}`,children:[e&&(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"bg-orange-500 rounded-xl p-2 flex items-center justify-center rounded-lg",children:(0,r.jsx)(o.A,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-orange-500 text-lg font-bold",children:"EX"}),(0,r.jsx)("span",{className:"text-[#005071] text-lg font-bold",children:"Learn"})]})]}),(0,r.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>t(!e),className:"p-2 hover:bg-gray-100","aria-label":e?"Collapse sidebar":"Expand sidebar",children:e?(0,r.jsx)(l.A,{className:"w-5 h-5"}):(0,r.jsx)(c.A,{className:"w-5 h-5"})})]}),(0,r.jsx)("nav",{className:"flex-1 p-4",children:(0,r.jsx)("div",{className:"space-y-2",children:h.map(t=>(0,r.jsxs)("a",{href:t.href,className:`flex items-center gap-3 py-1.5 px-3 rounded-lg text-[#005071] hover:text-white hover:bg-[#005071] transition-colors group ${e?"justify-start":"justify-center"}`,title:e?void 0:t.label,children:[(0,r.jsx)("span",{className:"transition-colors",children:t.icon}),e&&(0,r.jsx)("span",{className:"text-xs font-medium",children:t.label})]},t.href))})})]})}var g=s(6952),b=s(5885),p=s(7e3),v=s(7346),j=s(6189);let N=({sidebarExpanded:e})=>{let[t,s]=(0,a.useState)(!1),[n,l]=(0,a.useState)(null),c=(0,a.useRef)(null),d=(0,j.useRouter)(),m=e=>{try{let t=e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),s=decodeURIComponent(atob(t).split("").map(function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(s)}catch(e){return console.error("Error decoding JWT:",e),null}};(0,a.useEffect)(()=>{let e=localStorage.getItem("token");if(e){let t=m(e);t&&l({id:t.id,email:t.email,username:t.username,role:t.role,first_name:t.first_name||t.username,last_name:t.last_name||""})}},[]);let x=(e,t)=>(e?e.charAt(0).toUpperCase():"")+(t?t.charAt(0).toUpperCase():"")||"U";return(0,a.useEffect)(()=>{let e=e=>{c.current&&!c.current.contains(e.target)&&s(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),(0,r.jsx)("nav",{className:"bg-white shadow-sm border-b sticky top-0 z-30",children:(0,r.jsxs)("div",{className:"container mx-auto flex justify-between items-center py-1 px-4",children:[!e&&(0,r.jsxs)("div",{className:"flex items-center space-x-3 absolute left-2",children:[(0,r.jsx)("div",{className:"bg-orange-500 rounded-xl p-2 flex items-center justify-center rounded-lg",children:(0,r.jsx)(o.A,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"text-orange-500 text-lg font-bold",children:"EX"}),(0,r.jsx)("span",{className:"text-[#005071] text-lg font-bold",children:"Learn"})]})]}),(0,r.jsx)("div",{className:"flex-grow flex justify-center mx-4",children:(0,r.jsx)("div",{className:"relative w-full max-w-xs"})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(v.A,{}),(0,r.jsx)("div",{className:"relative",ref:c,children:n?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(i.$,{variant:"ghost",className:"flex items-center space-x-2 px-2 py-1 rounded-full hover:bg-orange-50",onClick:()=>{s(!t)},children:[(0,r.jsx)("span",{className:"bg-orange-500 text-white rounded-full h-8 w-8 flex items-center justify-center font-medium",children:x(n.first_name,n.last_name)}),(0,r.jsx)("span",{className:"text-gray-700 text-sm font-medium",children:n.first_name}),(0,r.jsx)(b.g,{icon:p.Jt$,className:`text-gray-500 text-xs transition-transform duration-200 ${t?"rotate-180":""}`})]}),t&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 animate-fade-in",children:[(0,r.jsx)("div",{className:"px-4 py-3 border-b border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"bg-orange-500 text-white rounded-full h-10 w-10 flex items-center justify-center font-medium",children:x(n.first_name,n.last_name)}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[n.first_name," ",n.last_name]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:n.email})]})]})}),(0,r.jsxs)("div",{className:"py-1",children:[(0,r.jsxs)(i.$,{variant:"ghost",className:"w-full flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-orange-50",onClick:()=>{s(!1)},children:[(0,r.jsx)(b.g,{icon:p.X46,className:"mr-3 text-gray-400"}),"Profile"]}),(0,r.jsxs)(i.$,{variant:"ghost",className:"w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50",onClick:()=>{localStorage.removeItem("token"),sessionStorage.removeItem("token"),localStorage.removeItem("authToken"),sessionStorage.removeItem("authToken"),l(null),d.push("/signin")},children:[(0,r.jsx)(b.g,{icon:p.GxD,className:"mr-3 text-red-500"}),"Log out"]})]})]})]}):(0,r.jsx)(i.$,{asChild:!0,className:"bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg",children:(0,r.jsxs)("a",{href:"/signin",className:"flex items-center space-x-2",children:[(0,r.jsx)(b.g,{icon:p.X46,className:"text-sm"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:"Sign In"})]})})})]})]})})};function y({children:e}){let[t,s]=n().useState(!0),a=(0,j.usePathname)();return["/","/courses","/courses/","/signin","/signin/","/signup","/signup/","/forgot-password","/forgot-password/","/my-dashboard","/my-dashboard/","/my-learning","/my-learning/","/my-certificates","/my-certificates/"].includes(a)||a.startsWith("/courses/")&&a.match(/^\/courses\/\d+(\/.*)?$/)?(0,r.jsx)(g.p,{children:e}):(0,r.jsxs)("div",{className:"flex min-h-screen",children:[(0,r.jsx)(f,{sidebarExpanded:t,setSidebarExpanded:s}),(0,r.jsx)("div",{className:"flex flex-col flex-1 min-h-screen",children:(0,r.jsxs)(g.p,{children:[(0,r.jsx)(N,{sidebarExpanded:t}),(0,r.jsx)("main",{className:"flex-1",children:e})]})})]})}},4367:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},4567:(e,t,s)=>{Promise.resolve().then(s.bind(s,534))},4780:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var r=s(9384),a=s(2348);function n(...e){return(0,a.QP)((0,r.$)(e))}},6952:(e,t,s)=>{"use strict";s.d(t,{E:()=>l,p:()=>o});var r=s(687),a=s(3210),n=s(7405);let i=(0,a.createContext)(void 0),o=({children:e})=>{let[t,s]=(0,a.useState)([]),[o,l]=(0,a.useState)(!1),[c,d]=(0,a.useState)(null);(0,a.useEffect)(()=>{let e=(0,n.io)("https://agenticairi-app6.exlservice.com/AITrainerbackend",{transports:["websocket","polling"],autoConnect:!0});return d(e),e.on("connect",()=>{console.log("Connected to notification server"),l(!0);let t=localStorage.getItem("token");if(t)try{let s=JSON.parse(atob(t.split(".")[1]));e.emit("join_user",s.id)}catch(e){console.error("Error decoding token:",e)}}),e.on("disconnect",()=>{console.log("Disconnected from notification server"),l(!1)}),e.on("new_notification",e=>{console.log("New notification received:",e),s(t=>[e,...t]),"granted"===Notification.permission&&new Notification(e.title,{body:e.message,icon:"/favicon.ico"})}),"default"===Notification.permission&&Notification.requestPermission(),m(),()=>{e.close()}},[]);let m=async()=>{try{let e=localStorage.getItem("token");if(!e)return;let t=await fetch("https://agenticairi-app6.exlservice.com/AITrainerbackend/api/notifications",{headers:{Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(t.ok){let e=await t.json();s(e)}}catch(e){console.error("Error fetching notifications:",e)}},x=t.filter(e=>!e.read).length;return(0,r.jsx)(i.Provider,{value:{notifications:t,unreadCount:x,isConnected:o,markAsRead:e=>{s(t=>t.map(t=>t.id===e?{...t,read:!0}:t))},markAllAsRead:()=>{s(e=>e.map(e=>({...e,read:!0})))},clearNotifications:()=>{s([])}},children:e})},l=()=>{let e=(0,a.useContext)(i);if(void 0===e)throw Error("useNotifications must be used within a NotificationProvider");return e}},7346:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(687),a=s(3210),n=s(5885),i=s(7e3),o=s(6952);let l=()=>{let[e,t]=(0,a.useState)(!1),s=(0,a.useRef)(null),{notifications:l,unreadCount:c,markAsRead:d,markAllAsRead:m,clearNotifications:x}=(0,o.E)();(0,a.useEffect)(()=>{let e=e=>{s.current&&!s.current.contains(e.target)&&t(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);let u=(e,t)=>{d(e),window.location.href=`/courses/${t}`},h=e=>{let t=new Date,s=new Date(e),r=Math.floor((t.getTime()-s.getTime())/6e4);return r<1?"Just now":r<60?`${r}m ago`:r<1440?`${Math.floor(r/60)}h ago`:`${Math.floor(r/1440)}d ago`};return(0,r.jsxs)("div",{className:"relative",ref:s,children:[(0,r.jsxs)("div",{className:"relative cursor-pointer hover:bg-gray-100 rounded-full p-2 transition-colors duration-200",onClick:()=>{t(!e)},children:[(0,r.jsx)(n.g,{icon:i.z$e,className:"text-gray-600 text-xl"}),c>0&&(0,r.jsx)("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium",children:c>99?"99+":c})]}),e&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden",children:[(0,r.jsxs)("div",{className:"px-4 py-3 border-b border-gray-100 flex justify-between items-center",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:"Notifications"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[c>0&&(0,r.jsxs)("button",{onClick:m,className:"text-xs text-blue-600 hover:text-blue-800 flex items-center",title:"Mark all as read",children:[(0,r.jsx)(n.g,{icon:i.e68,className:"mr-1"}),"Mark all read"]}),(0,r.jsx)("button",{onClick:x,className:"text-xs text-gray-500 hover:text-gray-700",title:"Clear all",children:(0,r.jsx)(n.g,{icon:i.GRI})})]})]}),(0,r.jsx)("div",{className:"max-h-80 overflow-y-auto",children:0===l.length?(0,r.jsxs)("div",{className:"px-4 py-8 text-center text-gray-500",children:[(0,r.jsx)(n.g,{icon:i.z$e,className:"text-3xl mb-2 opacity-50"}),(0,r.jsx)("p",{children:"No notifications yet"})]}):l.map(e=>(0,r.jsx)("div",{className:`px-4 py-3 border-b border-gray-50 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${!e.read?"bg-blue-50 border-l-4 border-l-blue-500":""}`,onClick:()=>u(e.id,e.courseId),children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"bg-orange-500 text-white rounded-full h-8 w-8 flex items-center justify-center",children:(0,r.jsx)(n.g,{icon:i.eST,className:"text-sm"})})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.message}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,r.jsx)("span",{className:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded",children:e.domain}),(0,r.jsx)("span",{className:"text-xs text-gray-400",children:h(e.timestamp)})]})]}),!e.read&&(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"})})]})},e.id))}),l.length>0&&(0,r.jsx)("div",{className:"px-4 py-2 border-t border-gray-100 text-center",children:(0,r.jsx)("button",{className:"text-sm text-blue-600 hover:text-blue-800 font-medium",children:"View All Notifications"})})]})]})}},7990:()=>{},8429:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u,metadata:()=>x});var r=s(7413),a=s(2376),n=s.n(a),i=s(8726),o=s.n(i),l=s(2587),c=s.n(l);s(1135);var d=s(5529);s(8380),d.$W.autoAddCss=!1,s(1120);var m=s(534);let x={title:"Create Next App",description:"Generated by create next app"};function u({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${c().variable} ${n().variable} ${o().variable} font-sans antialiased`,children:(0,r.jsx)(m.default,{children:e})})})}},9523:(e,t,s)=>{"use strict";s.d(t,{$:()=>l});var r=s(687);s(3210);var a=s(8730),n=s(4224),i=s(4780);let o=(0,n.F)("cursor-pointer inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"underline-offset-4 hover:underline text-primary",brand:"bg-brand-blue text-white border border-brand-blue hover:bg-brand-blue/90 hover:border-brand-blue/80",sucess:"bg-green-600 text-white border border-green-700 hover:bg-green-600/90 hover:border-green-600/80",warning:"bg-amber-400 text-gray-900 border border-yellow-500 hover:bg-yellow-400/90 hover:border-yellow-400/80"},size:{default:"h-10 py-2 px-4",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-md",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:s,asChild:n=!1,...l}){let c=n?a.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:s,className:e})),...l})}},9727:()=>{}};