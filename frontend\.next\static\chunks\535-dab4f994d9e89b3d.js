"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[535],{646:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},987:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("clapperboard",[["path",{d:"M20.2 6 3 11l-.9-2.4c-.3-1.1.3-2.2 1.3-2.5l13.5-4c1.1-.3 2.2.3 2.5 1.3Z",key:"1tn4o7"}],["path",{d:"m6.2 5.3 3.1 3.9",key:"iuk76l"}],["path",{d:"m12.4 3.4 3.1 4",key:"6hsd6n"}],["path",{d:"M3 11h18v8a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2Z",key:"ltgou9"}]])},2138:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},3311:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},4186:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5040:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5041:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("circle-user-round",[["path",{d:"M18 20a6 6 0 0 0-12 0",key:"1qehca"}],["circle",{cx:"12",cy:"10",r:"4",key:"1h16sb"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},5452:(e,t,n)=>{n.d(t,{UC:()=>ee,VY:()=>en,ZL:()=>X,bL:()=>Q,bm:()=>er,hE:()=>et,hJ:()=>$});var r=n(2115),a=n(5185),o=n(6101),l=n(6081),i=n(1285),s=n(5845),u=n(9178),d=n(7900),c=n(4378),p=n(8905),f=n(3655),m=n(2293),v=n(3795),y=n(8168),g=n(9708),h=n(5155),x="Dialog",[N,k]=(0,l.A)(x),[A,b]=N(x),D=e=>{let{__scopeDialog:t,children:n,open:a,defaultOpen:o,onOpenChange:l,modal:u=!0}=e,d=r.useRef(null),c=r.useRef(null),[p,f]=(0,s.i)({prop:a,defaultProp:null!=o&&o,onChange:l,caller:x});return(0,h.jsx)(A,{scope:t,triggerRef:d,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:r.useCallback(()=>f(e=>!e),[f]),modal:u,children:n})};D.displayName=x;var M="DialogTrigger";r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=b(M,n),i=(0,o.s)(t,l.triggerRef);return(0,h.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":V(l.open),...r,ref:i,onClick:(0,a.m)(e.onClick,l.onOpenToggle)})}).displayName=M;var w="DialogPortal",[O,R]=N(w,{forceMount:void 0}),j=e=>{let{__scopeDialog:t,forceMount:n,children:a,container:o}=e,l=b(w,t);return(0,h.jsx)(O,{scope:t,forceMount:n,children:r.Children.map(a,e=>(0,h.jsx)(p.C,{present:n||l.open,children:(0,h.jsx)(c.Z,{asChild:!0,container:o,children:e})}))})};j.displayName=w;var I="DialogOverlay",C=r.forwardRef((e,t)=>{let n=R(I,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,o=b(I,e.__scopeDialog);return o.modal?(0,h.jsx)(p.C,{present:r||o.open,children:(0,h.jsx)(T,{...a,ref:t})}):null});C.displayName=I;var E=(0,g.TL)("DialogOverlay.RemoveScroll"),T=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=b(I,n);return(0,h.jsx)(v.A,{as:E,allowPinchZoom:!0,shards:[a.contentRef],children:(0,h.jsx)(f.sG.div,{"data-state":V(a.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),_="DialogContent",P=r.forwardRef((e,t)=>{let n=R(_,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,o=b(_,e.__scopeDialog);return(0,h.jsx)(p.C,{present:r||o.open,children:o.modal?(0,h.jsx)(F,{...a,ref:t}):(0,h.jsx)(L,{...a,ref:t})})});P.displayName=_;var F=r.forwardRef((e,t)=>{let n=b(_,e.__scopeDialog),l=r.useRef(null),i=(0,o.s)(t,n.contentRef,l);return r.useEffect(()=>{let e=l.current;if(e)return(0,y.Eq)(e)},[]),(0,h.jsx)(U,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,a.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,a.m)(e.onFocusOutside,e=>e.preventDefault())})}),L=r.forwardRef((e,t)=>{let n=b(_,e.__scopeDialog),a=r.useRef(!1),o=r.useRef(!1);return(0,h.jsx)(U,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,l;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(a.current||null==(l=n.triggerRef.current)||l.focus(),t.preventDefault()),a.current=!1,o.current=!1},onInteractOutside:t=>{var r,l;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(a.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let i=t.target;(null==(l=n.triggerRef.current)?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),U=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:a,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,c=b(_,n),p=r.useRef(null),f=(0,o.s)(t,p);return(0,m.Oh)(),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(d.n,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,h.jsx)(u.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":V(c.open),...s,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(K,{titleId:c.titleId}),(0,h.jsx)(Y,{contentRef:p,descriptionId:c.descriptionId})]})]})}),S="DialogTitle",q=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=b(S,n);return(0,h.jsx)(f.sG.h2,{id:a.titleId,...r,ref:t})});q.displayName=S;var G="DialogDescription",W=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=b(G,n);return(0,h.jsx)(f.sG.p,{id:a.descriptionId,...r,ref:t})});W.displayName=G;var z="DialogClose",B=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=b(z,n);return(0,h.jsx)(f.sG.button,{type:"button",...r,ref:t,onClick:(0,a.m)(e.onClick,()=>o.onOpenChange(!1))})});function V(e){return e?"open":"closed"}B.displayName=z;var Z="DialogTitleWarning",[H,J]=(0,l.q)(Z,{contentName:_,titleName:S,docsSlug:"dialog"}),K=e=>{let{titleId:t}=e,n=J(Z),a="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(a))},[a,t]),null},Y=e=>{let{contentRef:t,descriptionId:n}=e,a=J("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(a.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(o))},[o,t,n]),null},Q=D,X=j,$=C,ee=P,et=q,en=W,er=B},5690:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},5863:(e,t,n)=>{n.d(t,{C1:()=>k,bL:()=>N});var r=n(2115),a=n(6081),o=n(3655),l=n(5155),i="Progress",[s,u]=(0,a.A)(i),[d,c]=s(i),p=r.forwardRef((e,t)=>{var n,r,a,i;let{__scopeProgress:s,value:u=null,max:c,getValueLabel:p=v,...f}=e;(c||0===c)&&!h(c)&&console.error((n="".concat(c),r="Progress","Invalid prop `max` of value `".concat(n,"` supplied to `").concat(r,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let m=h(c)?c:100;null===u||x(u,m)||console.error((a="".concat(u),i="Progress","Invalid prop `value` of value `".concat(a,"` supplied to `").concat(i,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let N=x(u,m)?u:null,k=g(N)?p(N,m):void 0;return(0,l.jsx)(d,{scope:s,value:N,max:m,children:(0,l.jsx)(o.sG.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":g(N)?N:void 0,"aria-valuetext":k,role:"progressbar","data-state":y(N,m),"data-value":null!=N?N:void 0,"data-max":m,...f,ref:t})})});p.displayName=i;var f="ProgressIndicator",m=r.forwardRef((e,t)=>{var n;let{__scopeProgress:r,...a}=e,i=c(f,r);return(0,l.jsx)(o.sG.div,{"data-state":y(i.value,i.max),"data-value":null!=(n=i.value)?n:void 0,"data-max":i.max,...a,ref:t})});function v(e,t){return"".concat(Math.round(e/t*100),"%")}function y(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function g(e){return"number"==typeof e}function h(e){return g(e)&&!isNaN(e)&&e>0}function x(e,t){return g(e)&&!isNaN(e)&&e<=t&&e>=0}m.displayName=f;var N=p,k=m},7580:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},8905:(e,t,n)=>{n.d(t,{C:()=>l});var r=n(2115),a=n(6101),o=n(331),l=e=>{let{present:t,children:n}=e,l=function(e){var t,n;let[a,l]=r.useState(),s=r.useRef(null),u=r.useRef(e),d=r.useRef("none"),[c,p]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=i(s.current);d.current="mounted"===c?e:"none"},[c]),(0,o.N)(()=>{let t=s.current,n=u.current;if(n!==e){let r=d.current,a=i(t);e?p("MOUNT"):"none"===a||(null==t?void 0:t.display)==="none"?p("UNMOUNT"):n&&r!==a?p("ANIMATION_OUT"):p("UNMOUNT"),u.current=e}},[e,p]),(0,o.N)(()=>{if(a){var e;let t,n=null!=(e=a.ownerDocument.defaultView)?e:window,r=e=>{let r=i(s.current).includes(e.animationName);if(e.target===a&&r&&(p("ANIMATION_END"),!u.current)){let e=a.style.animationFillMode;a.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===a.style.animationFillMode&&(a.style.animationFillMode=e)})}},o=e=>{e.target===a&&(d.current=i(s.current))};return a.addEventListener("animationstart",o),a.addEventListener("animationcancel",r),a.addEventListener("animationend",r),()=>{n.clearTimeout(t),a.removeEventListener("animationstart",o),a.removeEventListener("animationcancel",r),a.removeEventListener("animationend",r)}}p("ANIMATION_END")},[a,p]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{s.current=e?getComputedStyle(e):null,l(e)},[])}}(t),s="function"==typeof n?n({present:l.isPresent}):r.Children.only(n),u=(0,a.s)(l.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,a=r&&"isReactWarning"in r&&r.isReactWarning;return a?e.ref:(a=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof n||l.isPresent?r.cloneElement(s,{ref:u}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},9803:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]])},9870:(e,t,n)=>{n.d(t,{b:()=>u});var r=n(2115),a=n(3655),o=n(5155),l="horizontal",i=["horizontal","vertical"],s=r.forwardRef((e,t)=>{var n;let{decorative:r,orientation:s=l,...u}=e,d=(n=s,i.includes(n))?s:l;return(0,o.jsx)(a.sG.div,{"data-orientation":d,...r?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...u,ref:t})});s.displayName="Separator";var u=s}}]);