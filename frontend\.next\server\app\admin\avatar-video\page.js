(()=>{var e={};e.id=736,e.ids=[736],e.modules={13:(e,t,s)=>{"use strict";s.d(t,{J:()=>i});var a=s(687);s(3210);var r=s(8148),n=s(4780);function i({className:e,...t}){return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(1658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1820:e=>{"use strict";e.exports=require("os")},1860:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},2655:(e,t,s)=>{Promise.resolve().then(s.bind(s,4053))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},3997:e=>{"use strict";e.exports=require("tty")},4053:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>eB});var a=s(687),r=s(9523),n=s(13),i=s(5079),l=s(3210),o=s(4780);function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,o.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,o.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function u({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,o.cn)("leading-none font-semibold",e),...t})}function x({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,o.cn)("px-6",e),...t})}var m=s(8730);let p=(0,s(4224).F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function h({className:e,variant:t,asChild:s=!1,...r}){let n=s?m.DX:"span";return(0,a.jsx)(n,{"data-slot":"badge",className:(0,o.cn)(p({variant:t}),e),...r})}var g=s(1273),f=s(4163),v="Progress",[b,j]=(0,g.A)(v),[y,N]=b(v),w=l.forwardRef((e,t)=>{var s,r;let{__scopeProgress:n,value:i=null,max:l,getValueLabel:o=C,...d}=e;(l||0===l)&&!D(l)&&console.error((s=`${l}`,`Invalid prop \`max\` of value \`${s}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let c=D(l)?l:100;null===i||M(i,c)||console.error((r=`${i}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let u=M(i,c)?i:null,x=P(u)?o(u,c):void 0;return(0,a.jsx)(y,{scope:n,value:u,max:c,children:(0,a.jsx)(f.sG.div,{"aria-valuemax":c,"aria-valuemin":0,"aria-valuenow":P(u)?u:void 0,"aria-valuetext":x,role:"progressbar","data-state":A(u,c),"data-value":u??void 0,"data-max":c,...d,ref:t})})});w.displayName=v;var k="ProgressIndicator",_=l.forwardRef((e,t)=>{let{__scopeProgress:s,...r}=e,n=N(k,s);return(0,a.jsx)(f.sG.div,{"data-state":A(n.value,n.max),"data-value":n.value??void 0,"data-max":n.max,...r,ref:t})});function C(e,t){return`${Math.round(e/t*100)}%`}function A(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function P(e){return"number"==typeof e}function D(e){return P(e)&&!isNaN(e)&&e>0}function M(e,t){return P(e)&&!isNaN(e)&&e<=t&&e>=0}function S({className:e,value:t,...s}){return(0,a.jsx)(w,{"data-slot":"progress",className:(0,o.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...s,children:(0,a.jsx)(_,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})})}_.displayName=k;var I="horizontal",R=["horizontal","vertical"],z=l.forwardRef((e,t)=>{var s;let{decorative:r,orientation:n=I,...i}=e,l=(s=n,R.includes(s))?n:I;return(0,a.jsx)(f.sG.div,{"data-orientation":l,...r?{role:"none"}:{"aria-orientation":"vertical"===l?l:void 0,role:"separator"},...i,ref:t})});function O({className:e,orientation:t="horizontal",decorative:s=!0,...r}){return(0,a.jsx)(z,{"data-slot":"separator",decorative:s,orientation:t,className:(0,o.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...r})}z.displayName="Separator";var q=s(2688);let $=(0,q.A)("clapperboard",[["path",{d:"M20.2 6 3 11l-.9-2.4c-.3-1.1.3-2.2 1.3-2.5l13.5-4c1.1-.3 2.2.3 2.5 1.3Z",key:"1tn4o7"}],["path",{d:"m6.2 5.3 3.1 3.9",key:"iuk76l"}],["path",{d:"m12.4 3.4 3.1 4",key:"6hsd6n"}],["path",{d:"M3 11h18v8a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2Z",key:"ltgou9"}]]),T=(0,q.A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),G=(0,q.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),E=(0,q.A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]),V=(0,q.A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]),F=(0,q.A)("circle-user-round",[["path",{d:"M18 20a6 6 0 0 0-12 0",key:"1qehca"}],["circle",{cx:"12",cy:"10",r:"4",key:"1h16sb"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),L=(0,q.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),U=(0,q.A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]),W=(0,q.A)("video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]]),B=(0,q.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var H=s(569),Z=s(8599),J=s(6963),X=s(5551),K=s(1355),Y=s(2547),Q=s(5028),ee=s(6059),et=s(1359),es=s(2247),ea=s(3376),er="Dialog",[en,ei]=(0,g.A)(er),[el,eo]=en(er),ed=e=>{let{__scopeDialog:t,children:s,open:r,defaultOpen:n,onOpenChange:i,modal:o=!0}=e,d=l.useRef(null),c=l.useRef(null),[u,x]=(0,X.i)({prop:r,defaultProp:n??!1,onChange:i,caller:er});return(0,a.jsx)(el,{scope:t,triggerRef:d,contentRef:c,contentId:(0,J.B)(),titleId:(0,J.B)(),descriptionId:(0,J.B)(),open:u,onOpenChange:x,onOpenToggle:l.useCallback(()=>x(e=>!e),[x]),modal:o,children:s})};ed.displayName=er;var ec="DialogTrigger";l.forwardRef((e,t)=>{let{__scopeDialog:s,...r}=e,n=eo(ec,s),i=(0,Z.s)(t,n.triggerRef);return(0,a.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":n.open,"aria-controls":n.contentId,"data-state":eM(n.open),...r,ref:i,onClick:(0,H.m)(e.onClick,n.onOpenToggle)})}).displayName=ec;var eu="DialogPortal",[ex,em]=en(eu,{forceMount:void 0}),ep=e=>{let{__scopeDialog:t,forceMount:s,children:r,container:n}=e,i=eo(eu,t);return(0,a.jsx)(ex,{scope:t,forceMount:s,children:l.Children.map(r,e=>(0,a.jsx)(ee.C,{present:s||i.open,children:(0,a.jsx)(Q.Z,{asChild:!0,container:n,children:e})}))})};ep.displayName=eu;var eh="DialogOverlay",eg=l.forwardRef((e,t)=>{let s=em(eh,e.__scopeDialog),{forceMount:r=s.forceMount,...n}=e,i=eo(eh,e.__scopeDialog);return i.modal?(0,a.jsx)(ee.C,{present:r||i.open,children:(0,a.jsx)(ev,{...n,ref:t})}):null});eg.displayName=eh;var ef=(0,m.TL)("DialogOverlay.RemoveScroll"),ev=l.forwardRef((e,t)=>{let{__scopeDialog:s,...r}=e,n=eo(eh,s);return(0,a.jsx)(es.A,{as:ef,allowPinchZoom:!0,shards:[n.contentRef],children:(0,a.jsx)(f.sG.div,{"data-state":eM(n.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),eb="DialogContent",ej=l.forwardRef((e,t)=>{let s=em(eb,e.__scopeDialog),{forceMount:r=s.forceMount,...n}=e,i=eo(eb,e.__scopeDialog);return(0,a.jsx)(ee.C,{present:r||i.open,children:i.modal?(0,a.jsx)(ey,{...n,ref:t}):(0,a.jsx)(eN,{...n,ref:t})})});ej.displayName=eb;var ey=l.forwardRef((e,t)=>{let s=eo(eb,e.__scopeDialog),r=l.useRef(null),n=(0,Z.s)(t,s.contentRef,r);return l.useEffect(()=>{let e=r.current;if(e)return(0,ea.Eq)(e)},[]),(0,a.jsx)(ew,{...e,ref:n,trapFocus:s.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,H.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),s.triggerRef.current?.focus()}),onPointerDownOutside:(0,H.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,s=0===t.button&&!0===t.ctrlKey;(2===t.button||s)&&e.preventDefault()}),onFocusOutside:(0,H.m)(e.onFocusOutside,e=>e.preventDefault())})}),eN=l.forwardRef((e,t)=>{let s=eo(eb,e.__scopeDialog),r=l.useRef(!1),n=l.useRef(!1);return(0,a.jsx)(ew,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(r.current||s.triggerRef.current?.focus(),t.preventDefault()),r.current=!1,n.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(n.current=!0));let a=t.target;s.triggerRef.current?.contains(a)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),ew=l.forwardRef((e,t)=>{let{__scopeDialog:s,trapFocus:r,onOpenAutoFocus:n,onCloseAutoFocus:i,...o}=e,d=eo(eb,s),c=l.useRef(null),u=(0,Z.s)(t,c);return(0,et.Oh)(),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(Y.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:n,onUnmountAutoFocus:i,children:(0,a.jsx)(K.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":eM(d.open),...o,ref:u,onDismiss:()=>d.onOpenChange(!1)})}),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ez,{titleId:d.titleId}),(0,a.jsx)(eO,{contentRef:c,descriptionId:d.descriptionId})]})]})}),ek="DialogTitle",e_=l.forwardRef((e,t)=>{let{__scopeDialog:s,...r}=e,n=eo(ek,s);return(0,a.jsx)(f.sG.h2,{id:n.titleId,...r,ref:t})});e_.displayName=ek;var eC="DialogDescription",eA=l.forwardRef((e,t)=>{let{__scopeDialog:s,...r}=e,n=eo(eC,s);return(0,a.jsx)(f.sG.p,{id:n.descriptionId,...r,ref:t})});eA.displayName=eC;var eP="DialogClose",eD=l.forwardRef((e,t)=>{let{__scopeDialog:s,...r}=e,n=eo(eP,s);return(0,a.jsx)(f.sG.button,{type:"button",...r,ref:t,onClick:(0,H.m)(e.onClick,()=>n.onOpenChange(!1))})});function eM(e){return e?"open":"closed"}eD.displayName=eP;var eS="DialogTitleWarning",[eI,eR]=(0,g.q)(eS,{contentName:eb,titleName:ek,docsSlug:"dialog"}),ez=({titleId:e})=>{let t=eR(eS),s=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return l.useEffect(()=>{e&&(document.getElementById(e)||console.error(s))},[s,e]),null},eO=({contentRef:e,descriptionId:t})=>{let s=eR("DialogDescriptionWarning"),a=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${s.contentName}}.`;return l.useEffect(()=>{let s=e.current?.getAttribute("aria-describedby");t&&s&&(document.getElementById(t)||console.warn(a))},[a,e,t]),null},eq=s(1860);function e$({...e}){return(0,a.jsx)(ed,{"data-slot":"dialog",...e})}function eT({...e}){return(0,a.jsx)(ep,{"data-slot":"dialog-portal",...e})}function eG({className:e,...t}){return(0,a.jsx)(eg,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function eE({className:e,children:t,showCloseButton:s=!0,...r}){return(0,a.jsxs)(eT,{"data-slot":"dialog-portal",children:[(0,a.jsx)(eG,{}),(0,a.jsxs)(ej,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...r,children:[t,s&&(0,a.jsxs)(eD,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,a.jsx)(eq.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function eV({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function eF({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"dialog-footer",className:(0,o.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function eL({className:e,...t}){return(0,a.jsx)(e_,{"data-slot":"dialog-title",className:(0,o.cn)("text-lg leading-none font-semibold",e),...t})}function eU({className:e,...t}){return(0,a.jsx)(eA,{"data-slot":"dialog-description",className:(0,o.cn)("text-muted-foreground text-sm",e),...t})}function eW(){let[e,t]=(0,l.useState)([]),[s,o]=(0,l.useState)(null),[m,p]=(0,l.useState)([]),[g,f]=(0,l.useState)(null),[v,b]=(0,l.useState)(null),[j,y]=(0,l.useState)(!0),[N,w]=(0,l.useState)(!0),[k,_]=(0,l.useState)(!1),[C,A]=(0,l.useState)([]),[P,D]=(0,l.useState)(!1),[M,I]=(0,l.useState)({current:0,total:0}),[R,z]=(0,l.useState)(""),q=m.reduce((e,t)=>(e.find(e=>e.course_id===t.course_id)||e.push({course_id:t.course_id,course_title:t.course_title}),e),[]),H=m.filter(e=>e.course_id===Number(g)),Z=async()=>{D(!1),_(!0),A([]),I({current:0,total:0}),z("");try{let e=await fetch(`http://localhost:5001/api/course-content-versions/${v}`),t=await e.json(),a=Array.isArray(t.content)?t.content.length:0;I({current:0,total:a});let r=await fetch("http://localhost:5001/api/generate-course-videos",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedAvatarId:s,selectedVersionId:v})}),n=await r.json();if(!r.ok)throw Error(n.error||"Failed to initiate video generation");console.log("Generated Videos Data:",n.generated_videos),A(n.generated_videos||[])}catch(e){console.error("Video generation error:",e),alert(`Video generation failed: ${e.message||"Unknown error"}`)}finally{_(!1),z("")}};return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8 bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen",children:[(0,a.jsx)(d,{className:"mb-8 border-0 shadow-xl bg-white/80 backdrop-blur-sm",children:(0,a.jsxs)(x,{className:"p-8",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"w-20 h-20 bg-orange-400 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg",children:(0,a.jsx)($,{className:"w-10 h-10 text-white"})}),(0,a.jsx)("h2",{className:"text-4xl font-bold mb-3 text-gray-900 bg-brand-blue bg-clip-text text-transparent",children:"Medical Course Avatar & Video Generation"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Select a medical professional avatar and generate engaging videos from your approved summaries with cutting-edge AI technology"})]}),(0,a.jsx)(O,{className:"my-8"}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-6 mb-8",children:[(0,a.jsx)(d,{className:`p-4 transition-all duration-300 ${s?"border-blue-500 bg-blue-50 shadow-lg scale-105":"border-gray-200 hover:shadow-md"}`,children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:`w-12 h-12 rounded-xl flex items-center justify-center transition-colors ${s?"bg-blue-500":"bg-gray-200"}`,children:(0,a.jsx)(T,{className:`w-6 h-6 ${s?"text-white":"text-gray-400"}`})}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("div",{className:`font-semibold ${s?"text-blue-600":"text-gray-500"}`,children:"Select Avatar"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Choose your presenter"})]})]})}),(0,a.jsx)(G,{className:"w-6 h-6 text-gray-300"}),(0,a.jsx)(d,{className:`p-4 transition-all duration-300 ${v?"border-green-500 bg-green-50 shadow-lg scale-105":"border-gray-200 hover:shadow-md"}`,children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:`w-12 h-12 rounded-xl flex items-center justify-center transition-colors ${v?"bg-green-500":"bg-gray-200"}`,children:(0,a.jsx)(E,{className:`w-6 h-6 ${v?"text-white":"text-gray-400"}`})}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("div",{className:`font-semibold ${v?"text-green-600":"text-gray-500"}`,children:"Select Content"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Choose course material"})]})]})}),(0,a.jsx)(G,{className:"w-6 h-6 text-gray-300"}),(0,a.jsx)(d,{className:`p-4 transition-all duration-300 ${k?"border-amber-500 bg-amber-50 shadow-lg scale-105":"border-gray-200 hover:shadow-md"}`,children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:`w-12 h-12 rounded-xl flex items-center justify-center transition-colors ${k?"bg-amber-500":"bg-gray-200"}`,children:(0,a.jsx)(V,{className:`w-6 h-6 ${k?"text-white":"text-gray-400"}`})}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("div",{className:`font-semibold ${k?"text-amber-600":"text-gray-500"}`,children:"Generate Videos"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Create content"})]})]})})]})]})}),(0,a.jsxs)(d,{className:"mb-8 border-0 shadow-xl",children:[(0,a.jsx)(c,{className:"pb-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center",children:(0,a.jsx)(T,{className:"w-7 h-7 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(u,{className:"text-2xl text-gray-900",children:"Select Medical Professional Avatar"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Choose your video presenter from our collection of medical professionals"})]})]})}),(0,a.jsx)(x,{children:N?(0,a.jsx)("div",{className:"flex items-center justify-center py-16",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3 text-gray-500",children:[(0,a.jsx)("div",{className:"w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"}),(0,a.jsx)("span",{className:"text-lg",children:"Loading avatars..."})]})}):0===e.length?(0,a.jsxs)(d,{className:"p-12 text-center bg-gray-50",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDC65"}),(0,a.jsx)("div",{className:"text-gray-500 text-lg",children:"No avatars available."})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:e.map(e=>(0,a.jsx)(d,{className:`group relative cursor-pointer transition-all duration-300 hover:shadow-xl ${s===e.id?"border-2 border-blue-500 bg-blue-50 shadow-xl scale-105":"border hover:border-blue-300 hover:bg-blue-50/50 hover:scale-102"}`,onClick:()=>o(e.id),children:(0,a.jsxs)(x,{className:"p-6 text-center",style:{minHeight:280},children:[(0,a.jsx)("div",{className:`w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center transition-all ${s===e.id?"bg-blue-500 shadow-lg":"bg-orange-100 group-hover:bg-blue-100"}`,children:(0,a.jsx)(F,{className:`w-8 h-8 transition-colors ${s===e.id?"text-white":"text-orange-600 group-hover:text-blue-600"}`})}),(0,a.jsx)("h3",{className:"font-bold text-gray-900 mb-3 text-lg truncate",title:e.avatar_name,children:e.avatar_name}),(0,a.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,a.jsx)(h,{variant:"secondary",className:"text-xs",children:e.specialty||"N/A"}),(0,a.jsx)("div",{className:"text-xs text-gray-500 truncate",title:e.domain||"N/A",children:e.domain||"N/A"}),(0,a.jsxs)("div",{className:"text-xs text-orange-600 truncate",title:e.voice_id||"N/A",children:["Voice: ",e.voice_id||"N/A"]})]}),(0,a.jsx)(r.$,{variant:s===e.id?"default":"outline",className:`w-full transition-all duration-200 ${s===e.id?"bg-blue-600 hover:bg-blue-700 text-white shadow-md":"hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300"}`,children:s===e.id?(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(L,{className:"w-4 h-4 mr-2"}),"Selected"]}):"Select Avatar"})]})},e.id))})})]}),null!==s&&(0,a.jsxs)(d,{className:"mb-8 border-0 shadow-xl",children:[(0,a.jsx)(c,{className:"pb-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center",children:(0,a.jsx)(E,{className:"w-7 h-7 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(u,{className:"text-2xl text-gray-900",children:"Select Course Content for Video Generation"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Choose your course and approved version for video creation"})]})]})}),(0,a.jsx)(x,{children:j?(0,a.jsx)("div",{className:"flex items-center justify-center py-16",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3 text-gray-500",children:[(0,a.jsx)("div",{className:"w-8 h-8 border-2 border-gray-300 border-t-green-500 rounded-full animate-spin"}),(0,a.jsx)("span",{className:"text-lg",children:"Loading approved content versions..."})]})}):0===m.length?(0,a.jsxs)(d,{className:"p-12 text-center bg-gray-50",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDCDA"}),(0,a.jsx)("div",{className:"text-gray-500 text-lg",children:"No approved content versions available."})]}):(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(n.J,{htmlFor:"course",className:"text-base font-semibold text-gray-700",children:"Course Selection"}),(0,a.jsxs)(i.l6,{value:g||"",onValueChange:e=>{f(e),b(null)},children:[(0,a.jsx)(i.bq,{className:"w-full h-14 text-base focus:ring-2 focus:ring-green-200 border-2 rounded-xl",children:(0,a.jsx)(i.yv,{placeholder:"Choose a course to generate videos from"})}),(0,a.jsx)(i.gC,{children:q.map(e=>(0,a.jsx)(i.eb,{value:e.course_id.toString(),className:"text-base py-3",children:e.course_title},e.course_id))})]})]}),g&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(n.J,{htmlFor:"version",className:"text-base font-semibold text-gray-700",children:"Version Selection"}),(0,a.jsxs)(i.l6,{value:v||"",onValueChange:e=>{b(e)},children:[(0,a.jsx)(i.bq,{className:"w-full h-14 text-base focus:ring-2 focus:ring-green-200 border-2 rounded-xl",children:(0,a.jsx)(i.yv,{placeholder:"Select an approved version"})}),(0,a.jsx)(i.gC,{children:H.map(e=>(0,a.jsxs)(i.eb,{value:e.version_id.toString(),className:"text-base py-3",children:["Version ",e.version_number," (Approved: ",new Date(e.approved_at).toLocaleDateString(),")"]},e.version_id))})]})]})]})})]}),(0,a.jsxs)(d,{className:"border-0 shadow-xl",children:[(0,a.jsx)(c,{className:"pb-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-amber-100 rounded-xl flex items-center justify-center",children:(0,a.jsx)($,{className:"w-7 h-7 text-amber-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(u,{className:"text-2xl text-gray-900",children:"Video Generation Progress"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Generate professional video content from your selections"})]})]}),(0,a.jsx)(r.$,{variant:"warning",size:"lg",className:"flex items-center space-x-3 px-8 py-4 text-white font-semibold rounded-2xl shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105",onClick:()=>{if(!s||!v)return void alert("Please select both an avatar and a content version.");D(!0)},disabled:!s||!v||k,children:k?(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("svg",{className:"animate-spin w-6 h-6 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),(0,a.jsx)("span",{className:"text-lg",children:"Generating Videos..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)($,{className:"w-6 h-6"}),(0,a.jsx)("span",{className:"text-lg",children:"Generate All Videos"})]})})]})}),(0,a.jsxs)(x,{children:[k&&M.total>0&&(0,a.jsx)(d,{className:"mb-8 bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-200",children:(0,a.jsxs)(x,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("h4",{className:"font-bold text-blue-900 text-lg flex items-center",children:[(0,a.jsx)(U,{className:"w-5 h-5 mr-2"}),"Generation Progress"]}),(0,a.jsxs)(h,{variant:"secondary",className:"bg-blue-100 text-blue-800 font-semibold",children:[M.current," of ",M.total," completed"]})]}),(0,a.jsx)(S,{value:M.current/M.total*100,className:"h-3 mb-4"}),R&&(0,a.jsxs)("p",{className:"text-sm text-blue-700 flex items-center",children:[(0,a.jsx)(W,{className:"w-4 h-4 mr-2"}),"Currently generating: ",(0,a.jsx)("span",{className:"font-medium ml-1",children:R})]})]})}),0===C.length&&!k&&(0,a.jsxs)("div",{className:"text-center py-20",children:[(0,a.jsx)("div",{className:"w-32 h-32 bg-gradient-to-br from-gray-100 to-gray-200 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg",children:(0,a.jsx)($,{className:"w-16 h-16 text-gray-400"})}),(0,a.jsx)("h3",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Ready to Generate Videos"}),(0,a.jsx)("p",{className:"text-gray-600 max-w-lg mx-auto mb-8 text-lg leading-relaxed",children:'Select an avatar and course content, then click "Generate All Videos" to begin creating your professional video content with AI.'}),(!s||!v)&&(0,a.jsx)(d,{className:"bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-200 max-w-md mx-auto",children:(0,a.jsxs)(x,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-amber-700 mb-3",children:[(0,a.jsx)(B,{className:"w-6 h-6"}),(0,a.jsx)("span",{className:"font-semibold text-lg",children:"Selection Required"})]}),(0,a.jsx)("p",{className:"text-amber-600",children:"Please select both an avatar and course content to proceed with video generation."})]})}),s&&v&&(0,a.jsx)(d,{className:"bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200 max-w-md mx-auto",children:(0,a.jsxs)(x,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-green-700 mb-3",children:[(0,a.jsx)(L,{className:"w-6 h-6"}),(0,a.jsx)("span",{className:"font-semibold text-lg",children:"Ready to Generate!"})]}),(0,a.jsx)("p",{className:"text-green-600",children:"Avatar and content selected. Click the button above to start video generation."})]})})]}),C.length>0&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,a.jsx)(W,{className:"w-6 h-6 text-gray-600"}),(0,a.jsx)("h4",{className:"font-bold text-gray-800 text-xl",children:"Generated Videos"}),(0,a.jsxs)(h,{variant:"secondary",className:"bg-green-100 text-green-800",children:[C.length," videos processed"]})]}),(0,a.jsx)("div",{className:"grid gap-4",children:C.map((e,t)=>(0,a.jsx)(d,{className:"hover:shadow-lg transition-all duration-200 border-l-4 border-l-blue-500",children:(0,a.jsx)(x,{className:"p-6",children:(0,a.jsx)("div",{className:"flex items-start justify-between",children:(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:["success"===e.generation_status?(0,a.jsx)("div",{className:"w-5 h-5 bg-green-500 rounded-full mr-4 flex items-center justify-center",children:(0,a.jsx)(L,{className:"w-3 h-3 text-white"})}):"skipped_empty_script"===e.generation_status?(0,a.jsx)("div",{className:"w-5 h-5 bg-yellow-500 rounded-full mr-4 flex items-center justify-center",children:(0,a.jsx)(B,{className:"w-3 h-3 text-white"})}):(0,a.jsx)("div",{className:"w-5 h-5 bg-red-500 rounded-full mr-4"}),(0,a.jsx)("h5",{className:"font-bold text-gray-800 text-xl",children:e.title||`Chapter ${e.page}`})]}),(0,a.jsxs)(h,{variant:"outline",className:"mb-4",children:["Page ",e.page]}),"success"===e.generation_status&&(0,a.jsx)(h,{className:"bg-green-100 text-green-800 hover:bg-green-100",children:"✅ Generated Successfully"}),"skipped_empty_script"===e.generation_status&&(0,a.jsx)(h,{className:"bg-yellow-100 text-yellow-800 hover:bg-yellow-100",children:"⚠️ Skipped (Empty Script)"}),"failed"===e.generation_status&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h,{className:"bg-red-100 text-red-800 hover:bg-red-100",children:"❌ Generation Failed"}),(0,a.jsx)("p",{className:"text-sm text-red-600 mt-2 p-3 bg-red-50 rounded-lg border border-red-200",children:e.error_details||"Unknown error occurred during generation"})]})]})})})},t))})]})]})]}),(0,a.jsx)(e$,{open:P,onOpenChange:D,children:(0,a.jsxs)(eE,{className:"max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)(eV,{className:"text-center",children:[(0,a.jsx)("div",{className:"w-20 h-20 bg-gradient-to-br from-amber-400 to-orange-500 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg",children:(0,a.jsx)($,{className:"w-10 h-10 text-white"})}),(0,a.jsx)(eL,{className:"text-2xl font-bold text-gray-800 mb-4",children:"Generate All Videos?"}),(0,a.jsx)(eU,{asChild:!0,children:(0,a.jsxs)("p",{className:"text-gray-600 mb-6 text-lg leading-relaxed",children:["This will generate videos for all pages in the selected course content.",(0,a.jsx)("br",{}),"This process may take several minutes to complete."]})})]}),(0,a.jsx)(d,{className:"bg-gradient-to-r from-amber-50 to-orange-50 border-2 border-amber-200 mb-6",children:(0,a.jsxs)(x,{className:"p-6",children:[(0,a.jsx)("div",{className:"text-center font-bold mb-4 text-amber-900 text-lg",children:"Selected Configuration"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsx)(T,{className:"w-6 h-6 text-amber-600"}),(0,a.jsx)("p",{className:"font-medium text-amber-800",children:"Avatar"}),(0,a.jsx)("p",{className:"text-sm text-amber-700 break-words",children:e.find(e=>e.id===s)?.avatar_name})]})}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsx)(E,{className:"w-6 h-6 text-amber-600"}),(0,a.jsx)("p",{className:"font-medium text-amber-800",children:"Course"}),(0,a.jsx)("p",{className:"text-sm text-amber-700 break-words",children:q.find(e=>e.course_id===Number(g))?.course_title})]})}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsx)(U,{className:"w-6 h-6 text-amber-600"}),(0,a.jsx)("p",{className:"font-medium text-amber-800",children:"Version"}),(0,a.jsx)("p",{className:"text-sm text-amber-700 break-words",children:H.find(e=>e.version_id===v)?.version_number})]})})]})]})}),(0,a.jsxs)(eF,{className:"flex space-x-4",children:[(0,a.jsx)(r.$,{variant:"outline",className:"flex-1 h-12 text-base",onClick:()=>D(!1),children:"Cancel"}),(0,a.jsx)(r.$,{variant:"brand",className:"flex-1 h-12 text-base",onClick:Z,children:"Yes, Generate Videos"})]})]})})]})}function eB(){return(0,a.jsx)(eW,{})}},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>m,gC:()=>x,l6:()=>d,yv:()=>c});var a=s(687);s(3210);var r=s(19),n=s(5891),i=s(3964),l=s(3589),o=s(4780);function d({...e}){return(0,a.jsx)(r.bL,{"data-slot":"select",...e})}function c({...e}){return(0,a.jsx)(r.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:s,...i}){return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[s,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function x({className:e,children:t,position:s="popper",...n}){return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...n,children:[(0,a.jsx)(p,{}),(0,a.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(h,{})]})})}function m({className:e,children:t,...s}){return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(i.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:t})]})}function p({className:e,...t}){return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(l.A,{className:"size-4"})})}function h({className:e,...t}){return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(n.A,{className:"size-4"})})}},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6059:(e,t,s)=>{"use strict";s.d(t,{C:()=>i});var a=s(3210),r=s(8599),n=s(6156),i=e=>{let{present:t,children:s}=e,i=function(e){var t,s;let[r,i]=a.useState(),o=a.useRef(null),d=a.useRef(e),c=a.useRef("none"),[u,x]=(t=e?"mounted":"unmounted",s={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},a.useReducer((e,t)=>s[e][t]??e,t));return a.useEffect(()=>{let e=l(o.current);c.current="mounted"===u?e:"none"},[u]),(0,n.N)(()=>{let t=o.current,s=d.current;if(s!==e){let a=c.current,r=l(t);e?x("MOUNT"):"none"===r||t?.display==="none"?x("UNMOUNT"):s&&a!==r?x("ANIMATION_OUT"):x("UNMOUNT"),d.current=e}},[e,x]),(0,n.N)(()=>{if(r){let e,t=r.ownerDocument.defaultView??window,s=s=>{let a=l(o.current).includes(s.animationName);if(s.target===r&&a&&(x("ANIMATION_END"),!d.current)){let s=r.style.animationFillMode;r.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=s)})}},a=e=>{e.target===r&&(c.current=l(o.current))};return r.addEventListener("animationstart",a),r.addEventListener("animationcancel",s),r.addEventListener("animationend",s),()=>{t.clearTimeout(e),r.removeEventListener("animationstart",a),r.removeEventListener("animationcancel",s),r.removeEventListener("animationend",s)}}x("ANIMATION_END")},[r,x]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:a.useCallback(e=>{o.current=e?getComputedStyle(e):null,i(e)},[])}}(t),o="function"==typeof s?s({present:i.isPresent}):a.Children.only(s),d=(0,r.s)(i.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,s=t&&"isReactWarning"in t&&t.isReactWarning;return s?e.ref:(s=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof s||i.isPresent?a.cloneElement(o,{ref:d}):null};function l(e){return e?.animationName||"none"}i.displayName="Presence"},6118:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var a=s(5239),r=s(8088),n=s(8170),i=s.n(n),l=s(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["admin",{children:["avatar-video",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,7532)),"C:\\Projects\\AI-Trainer\\frontend\\src\\app\\admin\\avatar-video\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,8429)),"C:\\Projects\\AI-Trainer\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Projects\\AI-Trainer\\frontend\\src\\app\\admin\\avatar-video\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/avatar-video/page",pathname:"/admin/avatar-video",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},7532:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Projects\\\\AI-Trainer\\\\frontend\\\\src\\\\app\\\\admin\\\\avatar-video\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Projects\\AI-Trainer\\frontend\\src\\app\\admin\\avatar-video\\page.tsx","default")},7910:e=>{"use strict";e.exports=require("stream")},8354:e=>{"use strict";e.exports=require("util")},9021:e=>{"use strict";e.exports=require("fs")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9511:(e,t,s)=>{Promise.resolve().then(s.bind(s,7532))},9551:e=>{"use strict";e.exports=require("url")},9646:e=>{"use strict";e.exports=require("child_process")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,773,658,782,114],()=>s(6118));module.exports=a})();