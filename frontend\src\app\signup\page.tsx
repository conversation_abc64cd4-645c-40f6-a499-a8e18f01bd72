"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";

export default function SignUp() {
  const [form, setForm] = useState({
    first_name: "",
    last_name: "",
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
  });
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    if (form.password !== form.confirmPassword) {
      setError("Passwords do not match");
      return;
    }
    setLoading(true);
    try {
      const res = await fetch(process.env.NEXT_PUBLIC_END_POINT+"/api/register", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          first_name: form.first_name,
          last_name: form.last_name,
          username: form.username,
          email: form.email,
          password: form.password,
        }),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data.error || "Registration failed");
      router.push("/signin");
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-[#fafbfc]">
      <div className="flex flex-col items-center mb-8">
        <div className="bg-orange-500 rounded-lg p-3 mb-4">
          <svg width="32" height="32" fill="white" viewBox="0 0 24 24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/></svg>
        </div>
        <h1 className="text-3xl font-bold mb-2">Create your account</h1>
        <p className="text-gray-500">Join the AI Trainer platform</p>
      </div>
      <form onSubmit={handleSubmit} className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
        <h2 className="text-xl font-semibold mb-2">Sign Up</h2>
        <p className="text-gray-500 mb-4">Create your account to start learning or teaching</p>
        <div className="mb-4 flex gap-2">
          <div className="w-1/2">
            <label className="block mb-1 font-medium">First Name</label>
            <input type="text" name="first_name" className="w-full border rounded px-3 py-2" value={form.first_name} onChange={handleChange} required placeholder="John" />
          </div>
          <div className="w-1/2">
            <label className="block mb-1 font-medium">Last Name</label>
            <input type="text" name="last_name" className="w-full border rounded px-3 py-2" value={form.last_name} onChange={handleChange} required placeholder="Doe" />
          </div>
        </div>
        <div className="mb-4">
          <label className="block mb-1 font-medium">Username</label>
          <input type="text" name="username" className="w-full border rounded px-3 py-2" value={form.username} onChange={handleChange} required placeholder="johndoe" />
        </div>
        <div className="mb-4">
          <label className="block mb-1 font-medium">Email</label>
          <input type="email" name="email" className="w-full border rounded px-3 py-2" value={form.email} onChange={handleChange} required placeholder="<EMAIL>" />
        </div>
        <div className="mb-4">
          <label className="block mb-1 font-medium">I want to</label>
          <select className="w-full border rounded px-3 py-2 bg-white" disabled>
            <option>Learn (take courses)</option>
          </select>
        </div>
        <div className="mb-4">
          <label className="block mb-1 font-medium">Password</label>
          <input type="password" name="password" className="w-full border rounded px-3 py-2" value={form.password} onChange={handleChange} required placeholder="Enter your password" />
        </div>
        <div className="mb-4">
          <label className="block mb-1 font-medium">Confirm Password</label>
          <input type="password" name="confirmPassword" className="w-full border rounded px-3 py-2" value={form.confirmPassword} onChange={handleChange} required placeholder="Confirm your password" />
        </div>
        {error && <div className="text-red-500 mb-2">{error}</div>}
        <button type="submit" className="w-full bg-orange-500 text-white py-2 rounded mt-2 hover:bg-orange-600 transition" disabled={loading}>{loading ? "Creating Account..." : "Create Account"}</button>
        <div className="text-center mt-4 text-gray-600">
          Already have an account? <a href="/signin" className="text-orange-500 hover:underline">Sign in</a>
        </div>
      </form>
    </div>
  );
} 